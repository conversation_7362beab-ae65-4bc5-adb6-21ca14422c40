#include <QMessageBox>
#include <QInputDialog>
#include <QFileDialog>
#include <QSpinBox>
#include <QDialogButtonBox>
#include <QProgressDialog>
#include <QDateTime>
#include <QDir>
#include <QApplication>

#include "ui/main_window.h"
#include "utils/dialog_utils.h"
#include "utils/string_utils.h"
#include "core/project_manager.h"

namespace ui {

void MainWindow::openTaskManager() {
    // 检查模型是否已加载
    if (!videoProcessingCore->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    // 确保插件已加载
    if (!ensureTaskPluginsLoaded()) {
        utils::showScrollableMessageBox(this, tr("警告"),
            tr("无法加载任务插件，任务管理器可能无法正常工作。"),
            QMessageBox::Warning);
    }

    // 获取当前项目
    auto project = core::ProjectManager::get_instance().get_current_project();
    if (!project) {
        // 如果没有当前项目，创建一个新项目
        project = core::ProjectManager::get_instance().create_project();
    }

    taskManagerDialog = std::make_unique<TaskManagerDialog>(
        videoProcessingCore->get_ai_processor(),
        project,
        this
    );

    // 设置对话框为非模态，这样用户可以继续操作主窗口
    taskManagerDialog->setModal(false);

    // 刷新任务列表
    taskManagerDialog->refreshTaskList();

    // 显示对话框
    taskManagerDialog->show();
    taskManagerDialog->raise();
    taskManagerDialog->activateWindow();
}

void MainWindow::openFrameExtractionDialog() {
    // 检查是否有视频打开
    if (!videoProcessingCore->is_video_opened()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先打开视频文件！"),
            QMessageBox::Warning);
        return;
    }

    // 检查是否是摄像头或RTSP流
    if (videoProcessingCore->get_video_provider()->is_from_camera()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("抽帧功能仅支持视频文件，不支持摄像头或流媒体！"),
            QMessageBox::Warning);
        return;
    }

    // 检查帧存储路径是否设置
    if (recordingOutputPath.isEmpty()) {
        bool pathSet = false;
        while (!pathSet) {
            setRecordingPath();
            if (recordingOutputPath.isEmpty()) {
                int ret = QMessageBox::question(this, tr("设置存储路径"),
                    tr("抽帧功能需要设置存储路径。\n\n是否重新选择？"),
                    QMessageBox::Yes | QMessageBox::No);
                if (ret == QMessageBox::No) {
                    return;
                }
            } else {
                pathSet = true;
            }
        }
    }

    // 获取视频信息
    int totalFrames = videoProcessingCore->get_total_frames();
    double fps = videoProcessingCore->get_fps();
    int videoDurationSecs = static_cast<int>(totalFrames / fps);

    // 创建对话框
    QDialog dialog(this);
    dialog.setWindowTitle(tr("视频抽帧"));
    dialog.setMinimumWidth(400);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // 添加视频信息
    QLabel* infoLabel = new QLabel(tr("视频信息：\n总帧数：%1\n帧率：%2 FPS\n时长：%3 秒")
        .arg(totalFrames)
        .arg(fps, 0, 'f', 2)
        .arg(videoDurationSecs));
    layout->addWidget(infoLabel);

    // 添加分隔线
    QFrame* line = new QFrame();
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    layout->addWidget(line);

    // 添加抽帧设置
    QLabel* framesPerSecLabel = new QLabel(tr("每秒抽取帧数："));
    QSpinBox* framesPerSecSpinBox = new QSpinBox();
    framesPerSecSpinBox->setRange(1, static_cast<int>(fps));
    framesPerSecSpinBox->setValue(1);  // 默认每秒1帧
    framesPerSecSpinBox->setSuffix(tr(" 帧/秒"));

    QHBoxLayout* fpsLayout = new QHBoxLayout();
    fpsLayout->addWidget(framesPerSecLabel);
    fpsLayout->addWidget(framesPerSecSpinBox);
    layout->addLayout(fpsLayout);

    // // 添加存储路径信息
    // QLabel* pathLabel = new QLabel(tr("存储路径：%1").arg(recordingOutputPath));
    // layout->addWidget(pathLabel);
    
    // 存储路径输入和浏览按钮
    QHBoxLayout* outputFileLayout = new QHBoxLayout();
    QLabel* outputFileLabel = new QLabel(tr("存储路径:"), &dialog);
    QLineEdit* outputFileEdit = new QLineEdit(recordingOutputPath, &dialog);
    QPushButton* browseFileButton = new QPushButton(tr("浏览..."), &dialog);

    outputFileLayout->addWidget(outputFileLabel);
    outputFileLayout->addWidget(outputFileEdit);
    outputFileLayout->addWidget(browseFileButton);
    layout->addLayout(outputFileLayout);

    // 浏览按钮功能：弹出系统文件夹选择对话框
    connect(browseFileButton, &QPushButton::clicked, [&]() {
        QString dir = QFileDialog::getExistingDirectory(&dialog, tr("选择存储文件夹"), outputFileEdit->text());
        if (!dir.isEmpty()) {
            outputFileEdit->setText(dir);
        }
    });

    // 添加说明文本
    QLabel* noteLabel = new QLabel(tr("注意: 修改视频存储路径后，新的视频将保存到指定位置。"), &dialog);
    noteLabel->setStyleSheet("color: #666; font-style: italic;");
    layout->addWidget(noteLabel);

    // 添加按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    layout->addWidget(buttonBox);

    // 连接信号
    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        recordingOutputPath = outputFileEdit->text(); // 新增：保存用户选择的路径
        int framesPerSec = framesPerSecSpinBox->value();
        extractFrames(framesPerSec);
    }
}

void MainWindow::extractFrames(int framesPerSecond) {
    // 检查视频是否打开
    if (!videoProcessingCore->is_video_opened()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("没有打开的视频！"),
            QMessageBox::Warning);
        return;
    }
    // 检查存储路径
    if (recordingOutputPath.isEmpty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("未设置存储路径！"),
            QMessageBox::Warning);
        return;
    }

    // 创建进度对话框
    QProgressDialog progress(tr("正在抽取视频帧..."), tr("取消"), 0, 100, this);
    progress.setWindowModality(Qt::WindowModal);
    progress.setMinimumDuration(0);
    progress.setValue(0);
    progress.show();
    QApplication::processEvents();

    // 保存当前播放状态
    bool wasPlaying = isPlaying;
    if (isPlaying) {
        stopPlayback();
    }

    // 获取视频信息
    auto videoProvider = videoProcessingCore->get_video_provider();
    int totalFrames = videoProcessingCore->get_total_frames();
    double fps = videoProcessingCore->get_fps();
    int videoDurationSecs = static_cast<int>(totalFrames / fps);

    // 计算抽帧间隔
    int frameInterval = static_cast<int>(fps / framesPerSecond);
    if (frameInterval < 1) frameInterval = 1;

    // 创建存储目录
    QDir dir(recordingOutputPath);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("无法创建输出目录!"), QMessageBox::Critical);
            return;
        }
    }

    // 创建子目录，使用当前时间作为目录名
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString extractDirName = QString("extract_%1").arg(timestamp);
    if (!dir.mkdir(extractDirName)) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法创建抽帧子目录!"), QMessageBox::Critical);
        return;
    }

    QString extractPath = recordingOutputPath + "/" + extractDirName;

    // 重置视频到开头
    videoProvider->set_frame_position(0);

    // 开始抽帧
    int frameCount = 0;
    int extractedCount = 0;
    bool cancelled = false;

    while (frameCount < totalFrames) {
        // 检查是否取消
        if (progress.wasCanceled()) {
            cancelled = true;
            break;
        }

        // 更新进度
        int progressValue = (frameCount * 100) / totalFrames;
        progress.setValue(progressValue);
        progress.setLabelText(tr("正在抽取视频帧... %1/%2").arg(extractedCount).arg(totalFrames / frameInterval));
        QApplication::processEvents();

        // 读取当前帧
        cv::Mat frame;
        if (!videoProvider->read_frame(frame)) {
            break;
        }

        // 如果是抽帧帧号，存储当前帧
        if (frameCount % frameInterval == 0) {
            QString framePath = QString("%1/frame_%2.png")
                .arg(extractPath)
                .arg(extractedCount, 6, 10, QChar('0'));

#ifdef _WIN32
            std::string savePath = utils::utf8ToAnsi(framePath.toStdString());
            cv::imwrite(savePath, frame);
#else
            cv::imwrite(framePath.toStdString(), frame);
#endif
            extractedCount++;
        }

        frameCount++;
    }

    // 完成抽帧
    progress.setValue(100);

    // 显示结果
    if (!cancelled) {
        utils::showScrollableMessageBox(this, tr("抽帧完成"),
            tr("共抽取了 %1 帧图像\n存储在目录：%2")
                .arg(extractedCount)
                .arg(extractPath),
            QMessageBox::Information);
    } else {
        utils::showScrollableMessageBox(this, tr("抽帧取消"),
            tr("抽帧操作已取消。\n已抽取 %1 帧图像\n存储在目录：%2")
                .arg(extractedCount)
                .arg(extractPath),
            QMessageBox::Information);
    }

    // 重置视频到开头
    videoProvider->set_frame_position(0);

    // 如果之前在播放，恢复播放
    if (wasPlaying) {
        togglePlayback();
    }
}

} // namespace ui

