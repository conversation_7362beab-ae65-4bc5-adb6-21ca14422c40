# 设置Qt自动处理
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 查找Qt5包
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Gui)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_BINARY_DIR}
    ${OpenCV_INCLUDE_DIRS}
    ${AIVIDEOCORE_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/AiVideo/include
)

# 源文件
set(SOURCES
    main.cpp
    mainwindow.cpp
    videostreamwidget.cpp
    streammanager.cpp
    streamconfig.cpp
    ${CMAKE_SOURCE_DIR}/AiVideo/src/utils/q_plugin_renderer.cpp
)

# 头文件
set(HEADERS
    mainwindow.h
    videostreamwidget.h
    streammanager.h
    streamconfig.h
    ${CMAKE_SOURCE_DIR}/AiVideo/include/utils/q_plugin_renderer.h
)

# UI文件
set(UIS
    mainwindow.ui
)

# 资源文件
set(RESOURCES
    resources.qrc
)

# 添加可执行文件 (移除WIN32标志以显示控制台)
add_executable(AiVideoVS WIN32
    ${SOURCES}
    ${HEADERS}
    ${UIS}
    ${RESOURCES}
)

set(_boost_libs
    # Boost::boost
    AIDIBOOST::atomic
    AIDIBOOST::chrono
    AIDIBOOST::date_time
    AIDIBOOST::filesystem
    AIDIBOOST::log
    AIDIBOOST::log_setup
    AIDIBOOST::regex
    AIDIBOOST::system
    AIDIBOOST::thread
)

target_link_libraries(AiVideoVS PRIVATE
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Gui
    VisionFlow
    pybind11::embed
    Python3::Python
    ${AIVIDEOCORE_LIBRARIES}
)

# 链接 OpenCV 库
if(WIN32)
    target_link_libraries(AiVideoVS
        PUBLIC
        Opencv
    )
else()
    find_package(OpenCV CONFIG REQUIRED)
    target_link_libraries(AiVideoVS PUBLIC ${OpenCV_LIBS})
endif()

# 链接 Boost 库
find_package(boost_filesystem CONFIG REQUIRED)
target_link_libraries(AiVideoVS PUBLIC Boost::filesystem)
find_package(boost_log CONFIG REQUIRED)
target_link_libraries(AiVideoVS PUBLIC Boost::log)

# 链接 jsoncpp 库
find_package(jsoncpp CONFIG REQUIRED)
target_link_libraries(AiVideoVS PUBLIC JsonCpp::JsonCpp)

target_compile_definitions(AiVideoVS PRIVATE VFLOW_ENABLE_OPENCV NON_BLOCKING_MODE WIN32_LEAN_AND_MEAN)

# 安装目标
install(TARGETS AiVideoVS
    RUNTIME DESTINATION release
)
