﻿# Find AiVideoCore libraries using the provided CMake files
# This module defines:
#  AIVIDEOCORE_FOUND - System has AiVideoCore
#  AIVIDEOCORE_INCLUDE_DIRS - AiVideoCore include directories
#  AIVIDEOCORE_LIBRARIES - Libraries needed to use AiVideoCore

# Define the path to AiVideoCore
set(AIVIDEOCORE_ROOT_DIR "D:/AiVideoDev/new_version/aivideocore")
set(AIVIDEOCORE_BIN_DIR "${AIVIDEOCORE_ROOT_DIR}/bin")
set(AIVIDEOCORE_CMAKE_DIR "${AIVIDEOCORE_BIN_DIR}/lib/cmake")
set(AIVIDEOCORE_INCLUDE_DIR "${AIVIDEOCORE_BIN_DIR}/include")

# Check if the directory exists
if(NOT EXISTS ${AIVIDEOCORE_BIN_DIR})
    message(FATAL_ERROR "AiVideoCore bin directory not found at ${AIVIDEOCORE_BIN_DIR}")
endif()

if(NOT EXISTS ${AIVIDEOCORE_INCLUDE_DIR})
    message(FATAL_ERROR "AiVideoCore include directory not found at ${AIVIDEOCORE_INCLUDE_DIR}")
endif()

# Add the AiVideoCore CMake modules path
list(APPEND CMAKE_MODULE_PATH "${AIVIDEOCORE_CMAKE_DIR}")
list(APPEND CMAKE_PREFIX_PATH "${AIVIDEOCORE_BIN_DIR}")

# Define the core components
set(AIVIDEOCORE_COMPONENTS
    AiVideoCore
)

# Find each component
foreach(component ${AIVIDEOCORE_COMPONENTS})
    # Try to find the component using the provided CMake config
    if(EXISTS "${AIVIDEOCORE_CMAKE_DIR}/${component}Config.cmake")
        message(STATUS "Finding ${component} using CMake config")
        find_package(${component} QUIET)

        if(${component}_FOUND)
            message(STATUS "Found ${component} using CMake config")
            list(APPEND AIVIDEOCORE_LIBRARIES ${component})
        else()
            message(STATUS "Could not find ${component} using CMake config, falling back to manual import")
            # Fall back to manual import
            add_library(${component} SHARED IMPORTED)
            set_target_properties(
                ${component}
                PROPERTIES
                IMPORTED_LOCATION
                "${AIVIDEOCORE_BIN_DIR}/release/${component}.dll"
                IMPORTED_IMPLIB
                "${AIVIDEOCORE_BIN_DIR}/lib/${component}.lib"
                INTERFACE_INCLUDE_DIRECTORIES
                "${AIVIDEOCORE_INCLUDE_DIR}"
            )

            # Add to the list of libraries
            list(APPEND AIVIDEOCORE_LIBRARIES ${component})
        endif()
    else()
        message(STATUS "No CMake config found for ${component}, using manual import")
        # Create imported library manually
        add_library(${component} SHARED IMPORTED)
        set_target_properties(
            ${component}
            PROPERTIES
            IMPORTED_LOCATION
            "${AIVIDEOCORE_BIN_DIR}/release/${component}.dll"
            IMPORTED_IMPLIB
            "${AIVIDEOCORE_BIN_DIR}/lib/${component}.lib"
            INTERFACE_INCLUDE_DIRECTORIES
            "${AIVIDEOCORE_INCLUDE_DIR}"
        )

        # Add to the list of libraries
        list(APPEND AIVIDEOCORE_LIBRARIES ${component})
    endif()

    # Install the DLL to the release directory
    install(
        FILES
        "${AIVIDEOCORE_BIN_DIR}/release/${component}.dll"
        DESTINATION release
    )
endforeach()

# Set include directories
set(AIVIDEOCORE_INCLUDE_DIRS ${AIVIDEOCORE_INCLUDE_DIR})

message(${AIVIDEOCORE_LIBRARIES})
# Handle the QUIETLY and REQUIRED arguments and set AIVIDEOCORE_FOUND
include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(AiVideoCore
    DEFAULT_MSG
    AIVIDEOCORE_LIBRARIES
    AIVIDEOCORE_INCLUDE_DIRS)

mark_as_advanced(AIVIDEOCORE_INCLUDE_DIRS AIVIDEOCORE_LIBRARIES)

