#include <QMessageBox>
#include <QInputDialog>
#include <QFileDialog>
#include <QComboBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QDialogButtonBox>
#include <QProgressDialog>
#include <QDateTime>
#include <QDir>
#include <QApplication>

#include "ui/main_window.h"
#include "utils/dialog_utils.h"
#include "utils/string_utils.h"

namespace ui {

void MainWindow::openDetectionBasedExtractionDialog() {
    // 检查是否有视频打开
    if (!videoProcessingCore->is_video_opened()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先打开视频文件！"),
            QMessageBox::Warning);
        return;
    }

    // 检查是否是摄像头或RTSP流
    if (videoProcessingCore->get_video_provider()->is_from_camera()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("抽帧功能仅支持视频文件，不支持摄像头或流媒体！"),
            QMessageBox::Warning);
        return;
    }

    // 检查模型是否已加载
    if (!videoProcessingCore->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    // 检查帧存储路径是否设置
    if (recordingOutputPath.isEmpty()) {
        bool pathSet = false;
        while (!pathSet) {
            setRecordingPath();
            if (recordingOutputPath.isEmpty()) {
                int ret = QMessageBox::question(this, tr("设置存储路径"),
                    tr("抽帧功能需要设置存储路径。\n\n是否重新选择？"),
                    QMessageBox::Yes | QMessageBox::No);
                if (ret == QMessageBox::No) {
                    return;
                }
            } else {
                pathSet = true;
            }
        }
    }

    // 获取视频信息
    int totalFrames = videoProcessingCore->get_total_frames();
    double fps = videoProcessingCore->get_fps();
    int videoDurationSecs = static_cast<int>(totalFrames / fps);

    // 创建对话框
    QDialog dialog(this);
    dialog.setWindowTitle(tr("基于检测结果的视频抽帧"));
    dialog.setMinimumWidth(450);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // 添加视频信息
    QLabel* infoLabel = new QLabel(tr("视频信息：\n总帧数：%1\n帧率：%2 FPS\n时长：%3 秒")
        .arg(totalFrames)
        .arg(fps, 0, 'f', 2)
        .arg(videoDurationSecs));
    layout->addWidget(infoLabel);

    // 添加分隔线
    QFrame* line = new QFrame();
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    layout->addWidget(line);

    // 添加目标类别选择
    QLabel* classLabel = new QLabel(tr("目标类别："));
    QComboBox* classComboBox = new QComboBox();

    // 添加"任意类别"选项
    classComboBox->addItem(tr("任意类别"), "");

    // 使用用户输入的类别名称
    if (userDefinedClassNames.isEmpty()) {
        // 如果用户尚未定义类别名称，弹出对话框让用户输入
        bool ok;
        QString classNamesStr = QInputDialog::getText(this, tr("输入类别名称"),
                                                   tr("请输入目标类别名称，多个类别用逗号分隔："),
                                                   QLineEdit::Normal, "", &ok);
        if (ok && !classNamesStr.isEmpty()) {
            // 解析用户输入的类别名称
            userDefinedClassNames = classNamesStr.split(',', Qt::SkipEmptyParts);
            // 去除每个类别名称前后的空白字符
            for (int i = 0; i < userDefinedClassNames.size(); ++i) {
                userDefinedClassNames[i] = userDefinedClassNames[i].trimmed();
            }
        }
    }

    // 添加用户定义的类别
    for (const auto& className : userDefinedClassNames) {
        classComboBox->addItem(className, className);
    }

    // 添加编辑类别按钮
    QPushButton* editClassesButton = new QPushButton(tr("编辑类别"));
    connect(editClassesButton, &QPushButton::clicked, [this, classComboBox]() {
        bool ok;
        QString classNamesStr = QInputDialog::getText(this, tr("编辑类别名称"),
                                                   tr("请输入目标类别名称，多个类别用逗号分隔："),
                                                   QLineEdit::Normal,
                                                   userDefinedClassNames.join(","), &ok);
        if (ok) {
            // 清除当前类别列表（保留“任意类别”选项）
            while (classComboBox->count() > 1) {
                classComboBox->removeItem(1);
            }

            // 解析用户输入的类别名称
            userDefinedClassNames = classNamesStr.split(',', Qt::SkipEmptyParts);
            // 去除每个类别名称前后的空白字符
            for (int i = 0; i < userDefinedClassNames.size(); ++i) {
                userDefinedClassNames[i] = userDefinedClassNames[i].trimmed();
            }

            // 添加新的类别选项
            for (const auto& className : userDefinedClassNames) {
                classComboBox->addItem(className, className);
            }

            // 保存设置
            saveSettings();
        }
    });

    QHBoxLayout* classLayout = new QHBoxLayout();
    classLayout->addWidget(classLabel);
    classLayout->addWidget(classComboBox);
    classLayout->addWidget(editClassesButton);
    layout->addLayout(classLayout);

    // 添加包含/不包含选择
    QLabel* includeLabel = new QLabel(tr("抽取条件："));
    QRadioButton* includeRadio = new QRadioButton(tr("包含目标"));
    QRadioButton* excludeRadio = new QRadioButton(tr("不包含目标"));
    includeRadio->setChecked(true);  // 默认选择包含目标

    QButtonGroup* includeGroup = new QButtonGroup();
    includeGroup->addButton(includeRadio);
    includeGroup->addButton(excludeRadio);

    QHBoxLayout* includeLayout = new QHBoxLayout();
    includeLayout->addWidget(includeLabel);
    includeLayout->addWidget(includeRadio);
    includeLayout->addWidget(excludeRadio);
    layout->addLayout(includeLayout);

    // 添加存储路径信息
    QLabel* pathLabel = new QLabel(tr("存储路径：%1").arg(recordingOutputPath));
    layout->addWidget(pathLabel);

    // 添加按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    layout->addWidget(buttonBox);

    // 连接信号
    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        QString targetClass = classComboBox->currentData().toString();
        bool includeTarget = includeRadio->isChecked();
        extractFramesByDetection(targetClass, includeTarget);
    }
}

void MainWindow::extractFramesByDetection(const QString& targetClass, bool includeTarget) {
    // 检查视频是否打开
    if (!videoProcessingCore->is_video_opened()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("没有打开的视频！"),
            QMessageBox::Warning);
        return;
    }

    // 检查模型是否已加载
    if (!videoProcessingCore->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    // 检查存储路径
    if (recordingOutputPath.isEmpty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("未设置存储路径！"),
            QMessageBox::Warning);
        return;
    }

    // 创建进度对话框
    QProgressDialog progress(tr("正在抽取视频帧..."), tr("取消"), 0, 100, this);
    progress.setWindowModality(Qt::WindowModal);
    progress.setMinimumDuration(0);
    progress.setValue(0);
    progress.show();
    QApplication::processEvents();

    // 保存当前播放状态
    bool wasPlaying = isPlaying;
    if (isPlaying) {
        stopPlayback();
    }

    // 创建子目录，使用当前时间作为目录名
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString condition = includeTarget ? "with" : "without";
    QString targetClassStr = targetClass.isEmpty() ? "any" : targetClass;
    QString extractDirName = QString("extract_%1_%2_%3").arg(timestamp).arg(condition).arg(targetClassStr);

    QDir dir(recordingOutputPath);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("无法创建输出目录!"), QMessageBox::Critical);
            return;
        }
    }

    if (!dir.mkdir(extractDirName)) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法创建抽帧子目录!"), QMessageBox::Critical);
        return;
    }

    QString extractPath = recordingOutputPath + "/" + extractDirName;

    // 使用VideoProcessingCore的extract_frames_by_detection方法
    int extractedCount = 0;
    try {
        extractedCount = videoProcessingCore->extract_frames_by_detection(
            extractPath.toStdString(),
            targetClass.toStdString(),
            includeTarget,
            [&progress](int progressPercent, int currentCount) -> bool {
                progress.setValue(progressPercent);
                progress.setLabelText(tr("正在抽取视频帧... 已提取: %1").arg(currentCount));
                QApplication::processEvents();
                return !progress.wasCanceled();
            }
        );
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("抽帧过程中发生错误：%1").arg(e.what()),
            QMessageBox::Critical);

        // 如果之前在播放，恢复播放
        if (wasPlaying) {
            togglePlayback();
        }
        return;
    }

    // 完成抽帧
    progress.setValue(100);

    // 显示结果
    if (!progress.wasCanceled()) {
        QString targetDesc = targetClass.isEmpty() ? tr("任意目标") : targetClass;
        QString conditionDesc = includeTarget ? tr("包含") : tr("不包含");

        utils::showScrollableMessageBox(this, tr("抽帧完成"),
            tr("共抽取了 %1 帧%2 %3 的图像\n存储在目录：%4")
                .arg(extractedCount)
                .arg(conditionDesc)
                .arg(targetDesc)
                .arg(extractPath),
            QMessageBox::Information);
    } else {
        utils::showScrollableMessageBox(this, tr("抽帧取消"),
            tr("抽帧操作已取消。\n已抽取部分图像\n存储在目录：%1")
                .arg(extractPath),
            QMessageBox::Information);
    }

    // 如果之前在播放，恢复播放
    if (wasPlaying) {
        togglePlayback();
    }
}

} // namespace ui
