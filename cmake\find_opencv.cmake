﻿include(FetchContent)
set(OPENCV_DEPS_ROOT "https://pan.aqrose.com/f/ba0fc844aa554863a3c8/?dl=1&p=opencv-4.13.0-gpu-win-vc16-x64.zip")

FetchContent_Declare(
  opencv
  URL ${OPENCV_DEPS_ROOT}
  UPDATE_DISCONNECTED
)

FetchContent_GetProperties(opencv)

if(NOT opencv_POPULATED)
    FetchContent_Populate(opencv)
    add_library(Opencv SHARED IMPORTED)
	set_target_properties(
        Opencv
		PROPERTIES
        IMPORTED_IMPLIB
        ${opencv_SOURCE_DIR}/x64/vc16/lib/opencv_world4130.lib
        IMPORTED_IMPLIB_DEBUG
        ${opencv_SOURCE_DIR}/x64/vc16/lib/opencv_world4130.lib
        IMPORTED_IMPLIB_RELEASE
        ${opencv_SOURCE_DIR}/x64/vc16/lib/opencv_world4130.lib
		IMPORTED_LOCATION
        ${opencv_SOURCE_DIR}/x64/vc16/bin/opencv_world4130.dll
		IMPORTED_LOCATION_DEBUG
        ${opencv_SOURCE_DIR}/x64/vc16/bin/opencv_world4130.dll
        IMPORTED_LOCATION_RELEASE
        ${opencv_SOURCE_DIR}/x64/vc16/bin/opencv_world4130.dll
		INTERFACE_INCLUDE_DIRECTORIES
        ${opencv_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:Opencv>
        ${opencv_SOURCE_DIR}/x64/vc16/bin/opencv_videoio_ffmpeg4130_64.dll
        DESTINATION release
    )
endif()
