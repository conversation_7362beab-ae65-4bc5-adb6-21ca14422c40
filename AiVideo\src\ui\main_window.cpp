﻿#include "ui/main_window.h"

#include <QAction>
#include <QApplication>
#include <QCoreApplication>
#include <QDateTime>
#include <QDebug>
#include <QDialogButtonBox>
#include <QDir>
#include <QDoubleSpinBox>
#include <QFileDialog>
#include <QFileInfo>
#include <QFrame>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QIcon>
#include <QImage>
#include <QInputDialog>
#include <QKeySequence>
#include <QMenuBar>
#include <QMessageBox>
#include <QProgressBar>
#include <QProgressDialog>
#include <QScrollArea>
#include <QStatusBar>
#include <QTextCodec>
#include <QProcess>
#include <QToolBar>
#include <QVBoxLayout>
#include <json/json.h>

#include <chrono>
#include <future>
#include <sstream>

#include "ai/plugins/ui/plugin_management_widget.h"
// #include "ai/plugins/ui/plugin_registration.h"
#include "ui/model_settings_dialog.h"
#include "utils/dialog_utils.h"
#include "utils/q_plugin_renderer.h"
#include "utils/string_utils.h"
#include "utils/settings_manager.h"

namespace ui {

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent),
      isPlaying(false),
      enableAI(false),
      frameSkipInterval(1),
      isRecording(false),
      isVideoRecording(false),
      fullscreenWindow(nullptr),
      frameCount(0),
      lastFpsUpdateTime(0),
      storedFrameCount(0),
      includeRenderingInSavedFrames(true),
      videoCodec(cv::VideoWriter::fourcc('H', '2', '6', '4')),
      videoFps(30.0),
      enableCounting(false),
      lastPrompt("fire"),
      lastScore(0.15),
      lastIou(70),
      enableResultStorage(false),
      resultStorageTcpPort(8888),
      resultStorageMode(core::VideoResultStorageServer::StorageMode::IMMEDIATE),
      resultStorageFlushInterval(5000),
      resultStorageProtocolType(core::protocols::ProtocolType::TCP),
      mqttBrokerAddress("localhost"),
      mqttBrokerPort(1883),
      mqttUsername(""),
      mqttPassword(""),
      mqttTopic("aivideo/results"),
      mqttQos(0),
      maxFrameHistory(DEFAULT_MAX_FRAME_HISTORY),
      videoStreamsContainer(nullptr),
      videoStreamsLayout(nullptr),
      isDragging(false),
      minimizeButton(nullptr),
      maximizeButton(nullptr),
      closeButton(nullptr),
      titleContainer(nullptr) {

    // 设置无标题栏窗口
    setWindowFlags(Qt::FramelessWindowHint);

    // 加载之前保存的设置
    loadSettings();

    // 初始化插件管理器
    pluginManager = std::make_shared<ai::plugins::PluginManager>("plugins");

    // 初始化核心组件
    videoProcessingCore = std::make_shared<core::VideoProcessingCore>();

    // 初始化vf环境
    core::VideoProcessingCore::initialize_visionflow("9733c801000702014f0d000200130023", "*************");

    // 在VisionFlow初始化后初始化Python帧处理器
    videoProcessingCore->initialize_python_frame_processor();

    // 初始化C++帧处理器
    videoProcessingCore->initialize_cpp_frame_processor();

    // 设置视窗标题和图标
    setWindowTitle(tr("阿丘视频AI分析平台VAS - 未命名项目"));
    resize(1280, 720);

    // 初始化项目相关变量
    currentProjectPath = "";
    recentProjectsMenu = nullptr;

    // 设置中央部件和背景
    QWidget *centralWidget = new QWidget(this);
    centralWidget->setObjectName("centralWidget");
    setCentralWidget(centralWidget);

    // 设置应用程序图标
    setWindowIcon(QIcon(":/rc/logo.ico"));

    // 设置状态栏
    statusBar()->showMessage(tr("就绪"), 3000);

    // 创建垂直布局作为主布局，用于包含标题栏、菜单栏和内容区域
    QVBoxLayout *mainVerticalLayout = new QVBoxLayout(centralWidget);
    mainVerticalLayout->setSpacing(0);  // 移除间距
    mainVerticalLayout->setContentsMargins(0, 0, 0, 0);  // 移除边距

    // 创建水平布局作为内容布局
    QWidget *contentWidget = new QWidget(centralWidget);
    QHBoxLayout *mainHorizontalLayout = new QHBoxLayout(contentWidget);
    mainHorizontalLayout->setSpacing(10);  // 减小间距
    mainHorizontalLayout->setContentsMargins(5, 5, 5, 5);  // 减小边距

    // 将内容区域添加到主布局
    mainVerticalLayout->addWidget(contentWidget);

    // 创建左侧和右侧面板
    QWidget *leftPanel = new QWidget(this);
    QWidget *rightPanel = new QWidget(this);

    // 设置面板样式
    leftPanel->setObjectName("leftPanel");
    rightPanel->setObjectName("rightPanel");

    // 创建布局
    QVBoxLayout *leftLayout = new QVBoxLayout(leftPanel);
    QVBoxLayout *rightLayout = new QVBoxLayout(rightPanel);
    leftLayout->setSpacing(8);
    rightLayout->setSpacing(8);
    leftLayout->setContentsMargins(5, 5, 5, 5);
    rightLayout->setContentsMargins(5, 5, 5, 5);

    // 设置面板布局的边距和间距
    leftLayout->setContentsMargins(0, 0, 0, 0);  // 移除边距
    rightLayout->setContentsMargins(0, 0, 0, 0);  // 移除边距
    leftLayout->setSpacing(5);  // 减小间距
    rightLayout->setSpacing(5);  // 减小间距

    // 标题栏将在setupMenuBar()方法中创建，放在菜单栏下方

    // 视频文件和模型选择区域 - 左侧面板顶部
    QVBoxLayout *fileSelectionLayout = new QVBoxLayout();

    // 视频文件选择 - 使用更现代的设计
    QGroupBox *fileGroupBox = new QGroupBox(tr("文件选择"), this);
    fileGroupBox->setObjectName("fileGroupBox");
    QVBoxLayout *fileGroupLayout = new QVBoxLayout(fileGroupBox);
    fileGroupLayout->setContentsMargins(10, 20, 10, 10);
    fileGroupLayout->setSpacing(10);

    // 视频文件选择
    QHBoxLayout *videoLayout = new QHBoxLayout();
    QLabel *videoLabel = new QLabel(tr("视频文件"), this);
    videoLabel->setMinimumWidth(80);
    videoPathEdit = new QLineEdit(this);
    videoPathEdit->setReadOnly(true);
    videoPathEdit->setPlaceholderText(tr("选择视频文件路径..."));
    QPushButton *browseVideoBtn = new QPushButton(tr("浏览"), this);
    browseVideoBtn->setProperty("class", "secondary"); // 使用次要按钮样式
    browseVideoBtn->setIcon(QIcon(":/rc/icons/file_icon.png"));
    browseVideoBtn->setIconSize(QSize(16, 16)); // 减小图标尺寸
    browseVideoBtn->setFixedSize(80, 28); // 设置固定大小
    videoLayout->addWidget(videoLabel);
    videoLayout->addWidget(videoPathEdit);
    videoLayout->addWidget(browseVideoBtn);

    // VisionFlow模型选择
    QHBoxLayout *modelLayout = new QHBoxLayout();
    QLabel *modelLabel = new QLabel(tr("AI模型文件"), this);
    modelLabel->setMinimumWidth(80);
    modelPathEdit = new QLineEdit(this);
    modelPathEdit->setReadOnly(true);
    modelPathEdit->setPlaceholderText(tr("选择AI模型文件路径..."));
    QPushButton *browseModelBtn = new QPushButton(tr("浏览"), this);
    browseModelBtn->setProperty("class", "secondary"); // 使用次要按钮样式
    browseModelBtn->setIcon(QIcon(":/rc/icons/file_icon.png"));
    browseModelBtn->setIconSize(QSize(16, 16)); // 减小图标尺寸
    browseModelBtn->setFixedSize(80, 28); // 设置固定大小
    modelLayout->addWidget(modelLabel);
    modelLayout->addWidget(modelPathEdit);
    modelLayout->addWidget(browseModelBtn);

    // 将视频和模型选择添加到文件组布局
    fileGroupLayout->addLayout(videoLayout);
    fileGroupLayout->addLayout(modelLayout);

    // 将文件组添加到左侧面板，设置为固定大小
    leftLayout->addWidget(fileGroupBox, 0); // 文件选择区域保持固定大小

    // 视频播放区域 - 左侧面板中间
    QGroupBox *videoPlayerGroup = new QGroupBox(tr("视频预览"), this);
    videoPlayerGroup->setObjectName("videoPlayerGroup");
    QVBoxLayout *playerLayout = new QVBoxLayout(videoPlayerGroup);
    playerLayout->setContentsMargins(10, 20, 10, 10);
    playerLayout->setSpacing(10);

    // 视频显示视图
    videoDisplay = new QGraphicsView(this);
    videoDisplay->setMinimumSize(400, 225); // 减小最小尺寸，允许更好的缩放
    videoDisplay->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置尺寸策略为可扩展
    videoDisplay->setAlignment(Qt::AlignCenter);
    videoDisplay->setObjectName("videoDisplay"); // 使用QSS中的videoDisplay ID样式
    videoDisplay->setRenderHint(QPainter::Antialiasing);
    videoDisplay->setRenderHint(QPainter::SmoothPixmapTransform);
    videoDisplay->setRenderHint(QPainter::TextAntialiasing);
    videoDisplay->setViewportUpdateMode(QGraphicsView::FullViewportUpdate);
    videoDisplay->setOptimizationFlags(QGraphicsView::DontSavePainterState);
    videoDisplay->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    videoDisplay->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    videoDisplay->setFrameShape(QFrame::NoFrame);

    // 创建初始场景
    QGraphicsScene* initialScene = new QGraphicsScene(this);
    QGraphicsTextItem* textItem = new QGraphicsTextItem(tr("加载视频后将在此处显示"));
    QFont font = textItem->font();
    font.setPointSize(14);
    textItem->setFont(font);
    textItem->setDefaultTextColor(Qt::white);
    initialScene->addItem(textItem);
    textItem->setPos(400 - textItem->boundingRect().width() / 2, 225 - textItem->boundingRect().height() / 2);
    videoDisplay->setScene(initialScene);

    // 视频播放控制
    QHBoxLayout *controlLayout = new QHBoxLayout();
    controlLayout->setObjectName("controlLayout");
    controlLayout->setSpacing(10);  // 增加按钮之间的间距
    controlLayout->setContentsMargins(10, 5, 10, 5);

    // 播放按钮
    playButton = new QPushButton(this);
    playButton->setObjectName("playButton");
    playButton->setText(tr("播放"));
    playButton->setIcon(QIcon(":/rc/icons/play_icon.png"));
    playButton->setIconSize(QSize(16, 16));  // 减小图标尺寸
    playButton->setFixedHeight(32);  // 设置固定高度
    playButton->setMinimumWidth(80); // 设置最小宽度

    // 停止按钮
    stopButton = new QPushButton(this);
    stopButton->setObjectName("stopButton");
    stopButton->setText(tr("停止"));
    stopButton->setIcon(QIcon(":/rc/icons/stop_icon.png"));
    stopButton->setIconSize(QSize(16, 16));  // 减小图标尺寸
    stopButton->setFixedHeight(32);  // 设置固定高度
    stopButton->setMinimumWidth(80); // 设置最小宽度

    // 全屏按钮
    fullscreenButton = new QPushButton(this);
    fullscreenButton->setObjectName("fullscreenButton");
    fullscreenButton->setText(tr("全屏"));
    fullscreenButton->setIcon(QIcon(":/rc/icons/fullscreen_icon.png"));
    fullscreenButton->setIconSize(QSize(16, 16));  // 减小图标尺寸
    fullscreenButton->setFixedHeight(32);  // 设置固定高度
    fullscreenButton->setMinimumWidth(80); // 设置最小宽度

    // 添加按钮到布局
    controlLayout->addStretch();  // 左侧弹簧
    controlLayout->addWidget(playButton);
    controlLayout->addWidget(stopButton);
    controlLayout->addWidget(fullscreenButton);
    controlLayout->addStretch();  // 右侧弹簧

    // 在controlLayout中添加时间标签
    currentTimeLabel = new QLabel(tr("00:00:00 / 00:00:00"), this);
    currentTimeLabel->setAlignment(Qt::AlignCenter);
    currentTimeLabel->setMinimumWidth(150);  // 设置最小宽度确保显示完整
    controlLayout->addWidget(currentTimeLabel);

    // 添加帧存储相关控件
    QVBoxLayout* recordingMainLayout = new QVBoxLayout();
    QHBoxLayout* recordingLayout = new QHBoxLayout();

    // 添加帧存储勾选框
    frameRecordingCheckBox = new QCheckBox(tr("播放时存储帧"), this);
    frameRecordingCheckBox->setToolTip(tr("开启后将存储当前播放的视频帧"));
    recordingLayout->addWidget(frameRecordingCheckBox);

    // 添加视频录制勾选框
    videoRecordingCheckBox = new QCheckBox(tr("播放时存储为视频"), this);
    videoRecordingCheckBox->setToolTip(tr("开启后将当前播放的视频帧实时存储为视频文件"));
    recordingLayout->addWidget(videoRecordingCheckBox);
    recordingLayout->addStretch();

    // 添加存储选项布局
    QHBoxLayout* recordingOptionsLayout = new QHBoxLayout();

    // 添加包含渲染结果勾选框
    QCheckBox* includeRenderingCheckBox = new QCheckBox(tr("包含渲染结果"), this);
    includeRenderingCheckBox->setToolTip(tr("开启后在存储的帧或视频中包含渲染结果"));
    includeRenderingCheckBox->setChecked(includeRenderingInSavedFrames);
    recordingOptionsLayout->addWidget(includeRenderingCheckBox);
    recordingOptionsLayout->addStretch();

    // 连接信号
    connect(includeRenderingCheckBox, &QCheckBox::toggled, [this](bool checked) {
        includeRenderingInSavedFrames = checked;
    });

    // 添加到主布局
    recordingMainLayout->addLayout(recordingLayout);
    recordingMainLayout->addLayout(recordingOptionsLayout);

    controlLayout->addLayout(recordingMainLayout);

    // 进度条
    videoSlider = new QSlider(Qt::Horizontal, this);
    videoSlider->setEnabled(false);

    // 创建视频信息区域
    QHBoxLayout *videoInfoLayout = new QHBoxLayout();

    // 添加帧率显示标签
    originalFpsLabel = new QLabel(tr("原始帧率: - FPS"), this);
    currentFpsLabel = new QLabel(tr("当前帧率: - FPS"), this);

    videoInfoLayout->addWidget(originalFpsLabel);
    videoInfoLayout->addWidget(currentFpsLabel);
    videoInfoLayout->addStretch();

    // 创建一个控制面板
    QWidget *controlPanel = new QWidget(this);
    controlPanel->setObjectName("controlPanel");
    QVBoxLayout *controlPanelLayout = new QVBoxLayout(controlPanel);
    controlPanelLayout->setContentsMargins(5, 5, 5, 5);
    controlPanelLayout->setSpacing(5);

    // 将控制元素添加到控制面板
    controlPanelLayout->addWidget(videoSlider);
    controlPanelLayout->addLayout(controlLayout);
    controlPanelLayout->addLayout(videoInfoLayout);

    // 将元素添加到播放器布局，设置视频显示区域的拉伸因子
    playerLayout->addWidget(videoDisplay, 1); // 视频显示区域占主要空间
    playerLayout->addWidget(controlPanel, 0); // 控制面板保持固定大小

    // 将视频播放器组添加到左侧面板，设置拉伸因子
    leftLayout->addWidget(videoPlayerGroup, 1); // 视频播放器组占主要空间

    // 移除底部弹性空间，让视频播放器组充分利用空间
    // leftLayout->addStretch(); // 注释掉这行

    // 处理设置区域 - 右侧面板
    // 添加处理模式选择
    QGroupBox *processingGroup = new QGroupBox(tr("任务处理"), this);
    processingGroup->setObjectName("processingGroup");
    QVBoxLayout *processingLayout = new QVBoxLayout(processingGroup);
    processingLayout->setContentsMargins(10, 20, 10, 10);
    processingLayout->setSpacing(10);

    // 添加任务管理按钮
    QPushButton *taskManagerButton = new QPushButton(tr("任务管理"), this);
    taskManagerButton->setIcon(QIcon(":/rc/icons/task_icon.png"));
    taskManagerButton->setIconSize(QSize(16, 16)); // 减小图标尺寸
    taskManagerButton->setFixedHeight(32); // 减小按钮高度
    connect(taskManagerButton, &QPushButton::clicked, this, &MainWindow::openTaskManager);
    processingLayout->addWidget(taskManagerButton);

    // 处理模式单选按钮
    QGroupBox *modeGroupBox = new QGroupBox(tr("处理模式"));
    modeGroupBox->setObjectName("modeGroupBox");
    QVBoxLayout *radioLayout = new QVBoxLayout(modeGroupBox);
    radioLayout->setContentsMargins(10, 20, 10, 10);
    radioLayout->setSpacing(10);

    processingModeGroup = new QButtonGroup(this);
    noProcessingRadio = new QRadioButton(tr("原始图像"), this);
    aiProcessingRadio = new QRadioButton(tr("AI任务处理"), this);

    // 设置单选按钮样式
    noProcessingRadio->setObjectName("processingRadio");
    aiProcessingRadio->setObjectName("processingRadio");

    processingModeGroup->addButton(noProcessingRadio, 0);
    processingModeGroup->addButton(aiProcessingRadio, 1);

    noProcessingRadio->setChecked(true);

    // 禁用AI相关选项，直到模型加载
    aiProcessingRadio->setEnabled(false);

    // 添加单选按钮到布局
    radioLayout->addWidget(noProcessingRadio);
    radioLayout->addWidget(aiProcessingRadio);

    // 添加模式组到处理布局
    processingLayout->addWidget(modeGroupBox);

    rightLayout->addWidget(processingGroup);

    // AI设置组
    QGroupBox *aiSettingsGroup = new QGroupBox(tr("AI处理设置"), this);
    aiSettingsGroup->setObjectName("aiSettingsGroup");
    QVBoxLayout *aiSettingsLayout = new QVBoxLayout(aiSettingsGroup);
    aiSettingsLayout->setContentsMargins(10, 20, 10, 10);
    aiSettingsLayout->setSpacing(10);

    // 添加帧历史设置
    QHBoxLayout *frameHistoryLayout = new QHBoxLayout();
    QLabel *frameHistoryLabel = new QLabel(tr("最大帧历史数量:"), this);
    frameHistoryLabel->setMinimumWidth(80);
    QSpinBox *frameHistorySpinBox = new QSpinBox(this);
    frameHistorySpinBox->setObjectName("frameHistorySpinBox");  // 设置对象名
    frameHistorySpinBox->setRange(1, 100);  // 设置合理的范围
    frameHistorySpinBox->setValue(maxFrameHistory);
    frameHistorySpinBox->setSuffix(tr(" 帧"));
    frameHistorySpinBox->setToolTip(tr("设置AI处理时保留的历史帧数量"));

    frameHistoryLayout->addWidget(frameHistoryLabel);
    frameHistoryLayout->addWidget(frameHistorySpinBox);
    frameHistoryLayout->addStretch();

    // 添加到AI设置布局
    aiSettingsLayout->addLayout(frameHistoryLayout);

    // 添加抽帧间隔设置
    QHBoxLayout *frameSkipLayout = new QHBoxLayout();
    QLabel *frameSkipLabel = new QLabel(tr("抽帧间隔:"), this);
    frameSkipLabel->setMinimumWidth(80);
    QSpinBox *frameSkipSpinBox = new QSpinBox(this);
    frameSkipSpinBox->setObjectName("frameSkipSpinBox");  // 设置对象名
    frameSkipSpinBox->setRange(1, 30);  // 设置合理的范围，最少1，最多30
    frameSkipSpinBox->setValue(frameSkipInterval);
    frameSkipSpinBox->setSuffix(tr(" 帧"));
    frameSkipSpinBox->setToolTip(tr("设置每隔多少帧进行一次AI检测，1表示每帧都检测"));

    frameSkipLayout->addWidget(frameSkipLabel);
    frameSkipLayout->addWidget(frameSkipSpinBox);
    frameSkipLayout->addStretch();

    // 添加到AI设置布局
    aiSettingsLayout->addLayout(frameSkipLayout);

    // 添加帧处理器配置按钮和状态显示
    QHBoxLayout *frameProcessorLayout = new QHBoxLayout();
    QPushButton *frameProcessorButton = new QPushButton(tr("帧处理器配置"), this);
    frameProcessorButton->setObjectName("frameProcessorButton");
    frameProcessorButton->setIcon(QIcon(":/rc/icons/settings_icon.png"));
    frameProcessorButton->setIconSize(QSize(16, 16)); // 减小图标尺寸
    frameProcessorButton->setFixedHeight(32); // 减小按钮高度

    // 添加帧处理器状态标签
    frameProcessorStatusLabel = new QLabel(tr("帧处理器: 未启用"), this);

    frameProcessorLayout->addWidget(frameProcessorButton);
    frameProcessorLayout->addWidget(frameProcessorStatusLabel);
    frameProcessorLayout->addStretch();

    // 添加到AI设置布局
    aiSettingsLayout->addLayout(frameProcessorLayout);

    // 连接帧处理器配置按钮的信号槽
    connect(frameProcessorButton, &QPushButton::clicked, [this]() {
        // 检查是否使用C++帧处理器
        if (videoProcessingCore && videoProcessingCore->get_ai_processor() &&
            videoProcessingCore->get_ai_processor()->is_using_cpp_frame_processor()) {
            // 打开C++帧处理器配置对话框
            openCppFrameProcessorDialog();
        } else {
            // 打开Python帧处理器配置对话框
            openPythonFrameProcessorDialog();
        }
    });

    // 连接信号槽
    connect(frameHistorySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &MainWindow::set_max_frame_history);

    // 连接抽帧间隔设置的信号槽
    connect(frameSkipSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            [this](int value) {
                frameSkipInterval = value;
                if (videoProcessingCore) {
                    videoProcessingCore->set_frame_skip_interval(value);
                }
            });

    // 添加分隔线
    QFrame *line = new QFrame(this);
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    aiSettingsLayout->addWidget(line);

    // 输入节点ID设置
    QHBoxLayout *inputIdLayout = new QHBoxLayout();
    QLabel *inputIdLabel = new QLabel(tr("输入节点ID:"), this);
    inputIdLabel->setMinimumWidth(80);
    inputNodeIdEdit = new QLineEdit("输入", this);
    inputNodeIdEdit->setObjectName("nodeIdEdit");
    inputNodeIdEdit->setPlaceholderText(tr("输入节点ID"));
    inputIdLayout->addWidget(inputIdLabel);
    inputIdLayout->addWidget(inputNodeIdEdit);

    // 输出节点ID设置
    QHBoxLayout *outputIdLayout = new QHBoxLayout();
    QLabel *outputIdLabel = new QLabel(tr("输出节点ID:"), this);
    outputIdLabel->setMinimumWidth(80);
    outputNodeIdEdit = new QLineEdit("分割/pred", this);
    outputNodeIdEdit->setObjectName("nodeIdEdit");
    outputNodeIdEdit->setPlaceholderText(tr("输出节点ID"));
    outputIdLayout->addWidget(outputIdLabel);
    outputIdLayout->addWidget(outputNodeIdEdit);

    // 添加应用按钮
    QHBoxLayout *nodeIdButtonLayout = new QHBoxLayout();
    QPushButton *applyNodeIdsButton = new QPushButton(tr("应用节点设置"), this);
    applyNodeIdsButton->setObjectName("applyNodeIdsButton");
    applyNodeIdsButton->setIcon(QIcon(":/rc/icons/settings_icon.png"));
    applyNodeIdsButton->setIconSize(QSize(16, 16)); // 减小图标尺寸
    applyNodeIdsButton->setFixedHeight(32); // 减小按钮高度
    connect(applyNodeIdsButton, &QPushButton::clicked, this, &MainWindow::updateNodeIds);
    nodeIdButtonLayout->addStretch();
    nodeIdButtonLayout->addWidget(applyNodeIdsButton);
    nodeIdButtonLayout->addStretch();

    // 添加测试按钮
    QPushButton *testAiButton = new QPushButton(tr("测试当前帧"), this);
    testAiButton->setObjectName("testAiButton");
    testAiButton->setIcon(QIcon(":/rc/icons/play_icon.png"));
    testAiButton->setIconSize(QSize(16, 16)); // 减小图标尺寸
    testAiButton->setFixedHeight(32); // 减小按钮高度
    connect(testAiButton, &QPushButton::clicked, this, &MainWindow::testCurrentFrameAI);

    aiSettingsLayout->addLayout(inputIdLayout);
    aiSettingsLayout->addLayout(outputIdLayout);
    aiSettingsLayout->addLayout(nodeIdButtonLayout);
    aiSettingsLayout->addWidget(testAiButton);
    aiSettingsGroup->setEnabled(false);

    rightLayout->addWidget(aiSettingsGroup);

    // 添加弹簧
    rightLayout->addStretch();

    // 将左右面板添加到主水平布局
    mainHorizontalLayout->addWidget(leftPanel, 4); // 左侧占4份宽度
    mainHorizontalLayout->addWidget(rightPanel, 1); // 右侧占1份宽度

    // 视频播放定时器
    playTimer = new QTimer(this);

    // 创建用于计算FPS的计时器
    fpsTimer = new QTimer(this);
    fpsTimer->setInterval(1000); // 每秒更新一次

    // 连接信号与槽
    connect(browseVideoBtn, &QPushButton::clicked, this, &MainWindow::browseVideoFile);
    connect(browseModelBtn, &QPushButton::clicked, this, &MainWindow::browseModelFile);
    connect(playButton, &QPushButton::clicked, this, &MainWindow::togglePlayback);
    connect(stopButton, &QPushButton::clicked, this, &MainWindow::stopPlayback);
    connect(playTimer, &QTimer::timeout, this, &MainWindow::updateVideoFrame);
    connect(videoSlider, &QSlider::sliderMoved, this, &MainWindow::seekVideo);
    connect(frameRecordingCheckBox, &QCheckBox::toggled, this, &MainWindow::toggleRecording);
    connect(videoRecordingCheckBox, &QCheckBox::toggled, this, &MainWindow::toggleVideoRecording);

    // 不再直接连接输入输出节点ID的文本框信号到updateNodeIds
    // 这样用户可以完成输入后再点击应用按钮进行更新

    // 处理模式选择连接
    connect(processingModeGroup, static_cast<void(QButtonGroup::*)(int)>(&QButtonGroup::idClicked),
            [=](int id) {
                // 禁用所有处理设置组
                aiSettingsGroup->setEnabled(false);
                enableAI = false;

                // 根据选择的处理方式启用对应设置
                if (id == 1) { // AI处理
                    aiSettingsGroup->setEnabled(true);
                    enableAI = true;

                    // 如果模型未加载，提示用户
                    if (!videoProcessingCore->is_model_loaded()) {
                        utils::showScrollableMessageBox(this, tr("警告"), tr("请先加载AI模型文件!"));
                    }
                }

                // 更新当前帧显示
                if (videoProcessingCore->is_video_opened()) {
                    cv::Mat frame;
                    videoProcessingCore->get_video_provider()->set_frame_position(videoProcessingCore->get_current_frame());
                    if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
                        updateDisplayFrame(frame);
                    }
                }
            });

    // 设置按钮文本
    playButton->setText(tr("播放"));
    stopButton->setText(tr("停止"));

    // 连接全屏按钮信号
    connect(fullscreenButton, &QPushButton::clicked, this, &MainWindow::toggleFullscreen);

    // 设置菜单栏
    setupMenuBar();

    // 设置 Python 脚本菜单
    setupPythonScriptMenu();

    // 初始化 Python 脚本管理器
    initializePythonScriptManager();

    // 更新帧处理器状态标签
    updateFrameProcessorStatus();

    // 在构造函数中添加fpsTimer信号连接
    connect(fpsTimer, &QTimer::timeout, this, &MainWindow::updateFpsDisplay);

    // setupPluginManagement();
}

MainWindow::~MainWindow() {
    // 停止帧录制
    if (isRecording) {
        stopRecording();
    }

    // 停止视频录制
    if (isVideoRecording) {
        stopVideoRecording();
    }

    // 保存设置
    saveSettings();

    // 停止播放
    stopPlayback();
}

void MainWindow::resizeEvent(QResizeEvent* event) {
    QMainWindow::resizeEvent(event);

    // 如果视频已加载且有当前帧，则更新显示
    if (videoProcessingCore && videoProcessingCore->is_video_opened() && !currentFrameMat.empty()) {
        // 如果视频正在播放，不需要额外处理，因为播放过程中会自动更新帧
        // 如果视频暂停，则需要重新显示当前帧以适应新尺寸
        if (!isPlaying) {
            updateDisplayFrame(currentFrameMat);
        }
    }
}

void MainWindow::mousePressEvent(QMouseEvent* event) {
    // 允许在标题栏或菜单栏区域拖动窗口
    if (titleContainer) {
        // 查找headerContainer
        QWidget* headerContainer = nullptr;
        for (QObject* child : centralWidget()->children()) {
            if (child->objectName() == "headerContainer") {
                headerContainer = qobject_cast<QWidget*>(child);
                break;
            }
        }

        // 获取标题栏在窗口中的位置
        QRect titleBarRect = QRect(titleContainer->mapTo(this, QPoint(0, 0)), titleContainer->size());

        // 获取整个头部容器（包含标题栏和菜单栏）的位置
        QRect headerRect;
        if (headerContainer) {
            headerRect = QRect(headerContainer->mapTo(this, QPoint(0, 0)), headerContainer->size());
        }

        // 检查是否点击在标题栏区域，但不在窗口控制按钮上
        if ((headerRect.contains(event->pos()) || titleBarRect.contains(event->pos())) &&
            event->button() == Qt::LeftButton) {

            // 如果点击在标题栏上，检查是否点击在窗口控制按钮上
            if (titleBarRect.contains(event->pos())) {
                QPoint posInTitleBar = event->pos() - titleBarRect.topLeft();
                QRect controlButtonsArea(titleBarRect.width() - 130, 0, 130, titleBarRect.height());

                if (controlButtonsArea.contains(posInTitleBar)) {
                    // 点击在控制按钮上，不进行拖动
                    QMainWindow::mousePressEvent(event);
                    return;
                }
            }

            // 点击在标题栏或菜单栏的非按钮区域，允许拖动
            isDragging = true;
            dragPosition = event->globalPos() - frameGeometry().topLeft();
            event->accept();
            return; // 不再调用基类方法，避免事件被其他控件处理
        }
    }
    QMainWindow::mousePressEvent(event);
}

void MainWindow::mouseMoveEvent(QMouseEvent* event) {
    if (isDragging && (event->buttons() & Qt::LeftButton)) {
        move(event->globalPos() - dragPosition);
        event->accept();
    }
    QMainWindow::mouseMoveEvent(event);
}

void MainWindow::mouseReleaseEvent(QMouseEvent* event) {
    isDragging = false;
    QMainWindow::mouseReleaseEvent(event);
}

void MainWindow::minimizeWindow() {
    showMinimized();
}

void MainWindow::toggleMaximizeWindow() {
    if (isMaximized()) {
        showNormal();
        maximizeButton->setToolTip(tr("最大化"));
        maximizeButton->setText("□");
    } else {
        showMaximized();
        maximizeButton->setToolTip(tr("还原"));
        maximizeButton->setText("❐");
    }
}

void MainWindow::closeWindow() {
    close();
}

// setupMenuBar function has been moved to main_window_menu.cpp

void MainWindow::loadSettings() {
    auto& settings = utils::SettingsManager::get_instance();
    lastPrompt = QString::fromStdString(settings.value("model/prompt", "fire").toString());
    lastScore = settings.value("model/score", 0.15).toDouble();
    lastIou = settings.value("model/iou", 70).toInt();
    recordingOutputPath = QString::fromStdString(settings.value("app/recordingPath", "").toString());
    enableResultStorage = settings.value("app/enableResultStorage", false).toBool();
    resultStorageTcpPort = settings.value("app/resultStorageTcpPort", 8888).toInt();
    int storageMode = settings.value("app/resultStorageMode", 0).toInt();
    resultStorageMode = static_cast<core::VideoResultStorageServer::StorageMode>(storageMode);
    resultStorageFlushInterval = settings.value("app/resultStorageFlushInterval", 5000).toInt();
    int protocolType = settings.value("app/resultStorageProtocolType", 0).toInt();
    resultStorageProtocolType = static_cast<core::protocols::ProtocolType>(protocolType);

    // 加载Modbus寄存器映射
    modbusRegisterMap.clear();
    QString mapJson = QString::fromStdString(settings.value("app/modbusRegisterMap", "").toString());
    if (!mapJson.isEmpty()) {
        try {
            Json::Value root;
            Json::Reader reader;
            if (reader.parse(mapJson.toStdString(), root) && root.isObject()) {
                for (const auto& key : root.getMemberNames()) {
                    if (root[key].isUInt()) {
                        modbusRegisterMap[key] = root[key].asUInt();
                    }
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "Error parsing Modbus register map: " << e.what() << std::endl;
        }
    }

    // 加载MQTT配置
    mqttBrokerAddress = QString::fromStdString(settings.value("app/mqttBrokerAddress", "localhost").toString());
    mqttBrokerPort = settings.value("app/mqttBrokerPort", 1883).toInt();
    mqttUsername = QString::fromStdString(settings.value("app/mqttUsername", "").toString());
    mqttPassword = QString::fromStdString(settings.value("app/mqttPassword", "").toString());
    mqttTopic = QString::fromStdString(settings.value("app/mqttTopic", "aivideo/results").toString());
    mqttQos = settings.value("app/mqttQos", 0).toInt();
    maxFrameHistory = settings.value("app/maxFrameHistory", DEFAULT_MAX_FRAME_HISTORY).toInt();
    frameSkipInterval = settings.value("app/frameSkipInterval", 1).toInt();
    includeRenderingInSavedFrames = settings.value("app/includeRenderingInSavedFrames", true).toBool();

    // 加载视频录制相关设置
    videoOutputPath = QString::fromStdString(settings.value("app/videoOutputPath", "").toString());
    videoFps = settings.value("app/videoFps", 30.0).toDouble();
    videoCodec = settings.value("app/videoCodec", cv::VideoWriter::fourcc('H', '2', '6', '4')).toInt();

    // 加载用户定义的类别名称
    // 注意：新的SettingsManager不支持QStringList，需要使用JSON格式存储
    QString savedClassNamesJson = QString::fromStdString(settings.value("app/userDefinedClassNames", "").toString());
    if (!savedClassNamesJson.isEmpty()) {
        try {
            Json::Value root;
            Json::Reader reader;
            if (reader.parse(savedClassNamesJson.toStdString(), root) && root.isArray()) {
                userDefinedClassNames.clear();
                for (const auto& item : root) {
                    if (item.isString()) {
                        userDefinedClassNames.append(QString::fromStdString(item.asString()));
                    }
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "Error parsing user defined class names: " << e.what() << std::endl;
        }
    }

    if (videoProcessingCore) {
        videoProcessingCore->get_ai_processor()->set_max_frame_history(maxFrameHistory);
        videoProcessingCore->set_frame_skip_interval(frameSkipInterval);
    }

    // 更新UI控件的值（如果已经创建）
    QSpinBox* frameHistorySpinBox = findChild<QSpinBox*>("frameHistorySpinBox");
    if (frameHistorySpinBox) {
        frameHistorySpinBox->setValue(maxFrameHistory);
    }

    QSpinBox* frameSkipSpinBox = findChild<QSpinBox*>("frameSkipSpinBox");
    if (frameSkipSpinBox) {
        frameSkipSpinBox->setValue(frameSkipInterval);
    }

    // 初始化结果存储服务
    if (enableResultStorage && videoProcessingCore) {
        videoProcessingCore->start_result_storage_server(
            "results",
            resultStorageMode,
            resultStorageTcpPort,
            resultStorageFlushInterval
        );
    }
}

void MainWindow::saveSettings() {
    auto& settings = utils::SettingsManager::get_instance();
    settings.setValue("model/prompt", lastPrompt.toStdString());
    settings.setValue("model/score", lastScore);
    settings.setValue("model/iou", lastIou);
    settings.setValue("app/recordingPath", recordingOutputPath.toStdString());
    settings.setValue("app/enableResultStorage", enableResultStorage);
    settings.setValue("app/resultStorageTcpPort", resultStorageTcpPort);
    settings.setValue("app/resultStorageMode", static_cast<int>(resultStorageMode));
    settings.setValue("app/resultStorageFlushInterval", resultStorageFlushInterval);
    settings.setValue("app/resultStorageProtocolType", static_cast<int>(resultStorageProtocolType));

    // 保存Modbus寄存器映射
    Json::Value mapRoot;
    for (const auto& pair : modbusRegisterMap) {
        mapRoot[pair.first] = pair.second;
    }

    // 使用 stringstream 替代 writeString
    Json::StreamWriterBuilder writer;
    std::ostringstream oss;
    std::unique_ptr<Json::StreamWriter> streamWriter(writer.newStreamWriter());
    streamWriter->write(mapRoot, &oss);
    std::string mapJson = oss.str();

    settings.setValue("app/modbusRegisterMap", mapJson);

    // 保存MQTT配置
    settings.setValue("app/mqttBrokerAddress", mqttBrokerAddress.toStdString());
    settings.setValue("app/mqttBrokerPort", mqttBrokerPort);
    settings.setValue("app/mqttUsername", mqttUsername.toStdString());
    settings.setValue("app/mqttPassword", mqttPassword.toStdString());
    settings.setValue("app/mqttTopic", mqttTopic.toStdString());
    settings.setValue("app/mqttQos", mqttQos);

    settings.setValue("app/maxFrameHistory", maxFrameHistory);
    settings.setValue("app/frameSkipInterval", frameSkipInterval);
    settings.setValue("app/includeRenderingInSavedFrames", includeRenderingInSavedFrames);

    // 保存视频录制相关设置
    settings.setValue("app/videoOutputPath", videoOutputPath.toStdString());
    settings.setValue("app/videoFps", videoFps);
    settings.setValue("app/videoCodec", videoCodec);

    // 保存用户定义的类别名称
    if (!userDefinedClassNames.isEmpty()) {
        Json::Value classNamesArray(Json::arrayValue);
        for (const QString& className : userDefinedClassNames) {
            classNamesArray.append(className.toStdString());
        }

        Json::StreamWriterBuilder classNamesWriter;
        std::ostringstream classNamesOss;
        std::unique_ptr<Json::StreamWriter> classNamesStreamWriter(classNamesWriter.newStreamWriter());
        classNamesStreamWriter->write(classNamesArray, &classNamesOss);
        std::string classNamesJson = classNamesOss.str();

        settings.setValue("app/userDefinedClassNames", classNamesJson);
    }
}

void MainWindow::set_max_frame_history(int count) {
    if (count > 0) {
        maxFrameHistory = count;
        if (videoProcessingCore) {
            videoProcessingCore->get_ai_processor()->set_max_frame_history(count);
        }
        saveSettings();
    }
}

int MainWindow::getMaxFrameHistory() const {
    return maxFrameHistory;
}

void MainWindow::enableAIProcessingMode(bool enable) {
    if (enable && videoProcessingCore->is_model_loaded()) {
        // 启用AI处理按钮
        aiProcessingRadio->setEnabled(true);

        // 选中AI处理模式
        aiProcessingRadio->setChecked(true);
        enableAI = true;

        // 启用AI设置组
        QGroupBox* aiSettingsGroup = findChild<QGroupBox*>("aiSettingsGroup");
        if (aiSettingsGroup) {
            aiSettingsGroup->setEnabled(true);
        }

        // 更新当前帧显示
        if (videoProcessingCore->is_video_opened()) {
            cv::Mat frame;
            videoProcessingCore->get_video_provider()->set_frame_position(videoProcessingCore->get_current_frame());
            if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
                updateDisplayFrame(frame);
            }
        }

        std::cout << "AI processing mode enabled" << std::endl;
    } else {
        // 禁用AI处理
        enableAI = false;
        noProcessingRadio->setChecked(true);

        // 禁用AI设置组
        QGroupBox* aiSettingsGroup = findChild<QGroupBox*>("aiSettingsGroup");
        if (aiSettingsGroup) {
            aiSettingsGroup->setEnabled(false);
        }

        std::cout << "AI processing mode disabled" << std::endl;
    }
}

void MainWindow::browseVideoFile() {
    auto& settings = utils::SettingsManager::get_instance();
    QString lastVideoPath = QString::fromStdString(settings.value("lastVideoPath", QDir::homePath().toStdString()).toString());
    QString filePath = QFileDialog::getOpenFileName(this, tr("选择视频文件"),
                                                  lastVideoPath,
                                                  tr("视频文件 (*.mp4 *.avi *.mkv *.mov)"));
    if (!filePath.isEmpty()) {
        settings.setValue("lastVideoPath", QFileInfo(filePath).absolutePath().toStdString());
        videoPathEdit->setText(filePath);
        loadVideo(filePath);
    }
}

void MainWindow::browseModelFile() {
    auto& settings = utils::SettingsManager::get_instance();
    QString lastModelPath = QString::fromStdString(settings.value("lastModelPath", QDir::homePath().toStdString()).toString());
    QString filePath = QFileDialog::getOpenFileName(this, tr("选择AI模型"),
                                                  lastModelPath,
                                                  tr("VisionFlow模型 (*.vfmodel)"));
    if (!filePath.isEmpty()) {
        settings.setValue("lastModelPath", QFileInfo(filePath).absolutePath().toStdString());
        modelPathEdit->setText(filePath);
        loadModel(filePath);
    }
}

void MainWindow::loadVideo(const QString& filePath) {
    // 停止当前播放
    stopPlayback();

    // 清空历史信息缓存
    if (videoProcessingCore) {
        // 重置AI处理器中的计数和跟踪器状态
        auto aiProcessor = videoProcessingCore->get_ai_processor();
        if (aiProcessor) {
            std::cout << "重置AI处理器状态和计数信息" << std::endl;
            aiProcessor->reset_counts();

            // 清空帧内存中的历史帧和跟踪结果
            auto frameMemory = aiProcessor->get_frame_memory();
            if (frameMemory) {
                std::cout << "清空帧内存历史缓存" << std::endl;
                frameMemory->clear();
            }
        }

        // 如果结果存储服务器正在运行，停止并重新启动它
        auto resultServer = videoProcessingCore->get_result_storage_server();
        if (resultServer && resultServer->is_running()) {
            std::cout << "重启结果存储服务器以清空历史结果" << std::endl;
            int tcpPort = resultServer->get_port();
            auto storageMode = resultServer->get_storage_mode();
            int flushInterval = resultServer->get_flush_interval();
            std::string storagePath = resultServer->get_storage_path();

            // 停止当前服务器
            videoProcessingCore->stop_result_storage_server();

            // 重新启动服务器（这将创建新的存储）
            videoProcessingCore->start_result_storage_server(storagePath, storageMode, tcpPort, flushInterval);
        }
    }

    // 打开新的视频文件
    if (!videoProcessingCore->open_video_file(filePath.toStdString())) {
        utils::showScrollableMessageBox(this, tr("错误"), tr("无法打开视频文件！"), QMessageBox::Critical);
        return;
    }

    // 设置播放器滑块范围
    videoSlider->setRange(0, videoProcessingCore->get_total_frames() - 1);
    videoSlider->setValue(0);
    videoSlider->setEnabled(true);

    // 读取第一帧显示
    cv::Mat frame;
    if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
        updateDisplayFrame(frame);
    }

    // 更新按钮状态
    playButton->setEnabled(true);
    stopButton->setEnabled(true);

    // 获取视频的原始帧率
    originalFpsLabel->setText(tr("原始帧率: %1 FPS").arg(QString::number(videoProcessingCore->get_fps(), 'f', 1)));

    // 计算并显示视频总时长
    int totalFrames = videoProcessingCore->get_total_frames();
    double fps = videoProcessingCore->get_fps();
    int totalSeconds = static_cast<int>(totalFrames / fps);
    currentTimeLabel->setText(tr("00:00:00 / %1").arg(formatTime(totalSeconds)));

    // 更新窗口标题显示当前加载的视频文件
    setWindowTitle(tr("阿丘视频AI分析平台VAS - %1").arg(QFileInfo(filePath).fileName()));

    // 设置AI处理器的帧率
    videoProcessingCore->get_ai_processor()->set_fps(videoProcessingCore->get_fps());

    std::cout << "成功加载新视频并清空历史信息缓存: " << filePath.toStdString() << std::endl;
}

void MainWindow::open_camera() {
    // 弹出对话框让用户选择摄像头
    bool ok;
    int cameraId = QInputDialog::getInt(this, tr("选择摄像头"),
                                      tr("摄像头ID (通常默认摄像头为0):"), 0, 0, 10, 1, &ok);
    if (!ok) {
        return; // 用户取消了选择
    }

    // 停止当前播放
    stopPlayback();

    // 打开摄像头
    if (!videoProcessingCore->open_camera(cameraId)) {
        utils::showScrollableMessageBox(this, tr("错误"),
                                       tr("无法打开摄像头 ID %1！请确认摄像头连接正常。").arg(cameraId),
                                       QMessageBox::Critical);
        return;
    }

    // 由于摄像头没有总帧数，禁用进度条
    videoSlider->setEnabled(false);

    // 读取第一帧显示
    cv::Mat frame;
    if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
        updateDisplayFrame(frame);
    }
}

void MainWindow::open_rtsp_stream() {
    // 弹出对话框让用户输入RTSP URL
    bool ok;
    QString rtspUrl = QInputDialog::getText(this, tr("输入RTSP流地址"),
                                         tr("RTSP URL:"), QLineEdit::Normal,
                                         "rtsp://127.0.0.1:8554/test", &ok);
    if (!ok || rtspUrl.isEmpty()) {
        return; // 用户取消了输入或输入为空
    }

    // 停止当前播放
    stopPlayback();

    // 显示连接中对话框
    QProgressDialog progress(tr("正在连接RTSP流..."), tr("取消"), 0, 0, this);
    progress.setWindowModality(Qt::WindowModal);
    progress.setMinimumDuration(0);
    progress.show();
    QApplication::processEvents();

    // 打开RTSP流
    if (!videoProcessingCore->open_rtsp_stream(rtspUrl.toStdString())) {
        utils::showScrollableMessageBox(this, tr("错误"),
                                       tr("无法打开RTSP流！\n请确认URL正确且流媒体服务器可访问。"),
                                       QMessageBox::Critical);
        return;
    }

    // 更新视频路径显示
    videoPathEdit->setText(rtspUrl);

    // 由于流没有总帧数，禁用进度条
    videoSlider->setEnabled(false);

    // 读取第一帧显示
    cv::Mat frame;
    if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
        updateDisplayFrame(frame);
    }

    // 自动开始播放
    if (!isPlaying) {
        togglePlayback();
    }
}

void MainWindow::loadModel(const QString& filePath) {
    // 检查输出节点ID是否为万物检测大模型
    QString outputId = outputNodeIdEdit->text();
    QString inputId = inputNodeIdEdit->text();
    bool isVLMOutput = (outputId == "万物检测大模型/pred");

    // 检查输入输出节点ID是否为空
    if (inputId.isEmpty() || outputId.isEmpty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("输入和输出节点ID不能为空！"),
            QMessageBox::Warning);
        return;
    }

    QString promptValue = "fire";
    double scoreValue = 0.15;
    int iouValue = 70;

    // 加载之前保存的设置
    loadSettings();

    // 只有当输出节点是万物检测大模型时才显示参数设置对话框
    if (isVLMOutput) {
        ModelSettingsDialog dialog(lastPrompt, lastScore, lastIou, this);
        if (dialog.exec() != QDialog::Accepted) {
            return;
        }

        promptValue = dialog.getPromptValue();
        scoreValue = dialog.getScoreValue();
        iouValue = dialog.getIouValue();

        if (promptValue.isEmpty()) {
            promptValue = "fire";
        }

        lastPrompt = promptValue;
        lastScore = scoreValue;
        lastIou = iouValue;
        saveSettings();
    }

    // 加载并初始化模型
    try {
        if (videoProcessingCore->load_model(filePath.toStdString(), lastPrompt.toStdString(), lastScore, lastIou) &&
            videoProcessingCore->initialize_runtime(inputId.toStdString(), outputId.toStdString())) {

            // 启用相关功能按钮
            aiProcessingRadio->setEnabled(true);

            QString successMsg;
            if (isVLMOutput) {
                successMsg = tr("AI模型加载并初始化成功!\n\n"
                              "当前参数设置：\n"
                              "提示词: %1\n"
                              "置信度阈值: %2\n"
                              "IOU阈值: %3")
                              .arg(promptValue)
                              .arg(scoreValue)
                              .arg(iouValue);
            } else {
                successMsg = tr("AI模型加载并初始化成功!");
            }
            utils::showScrollableMessageBox(this, tr("成功"), successMsg, QMessageBox::Information);
        }
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("模型加载失败: %1").arg(e.what()),
            QMessageBox::Critical);
    }
}

void MainWindow::reconfigureModel() {
    if (!videoProcessingCore->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型!"),
            QMessageBox::Warning);
        return;
    }

    QString inputId = inputNodeIdEdit->text();
    QString outputId = outputNodeIdEdit->text();

    ModelSettingsDialog dialog(lastPrompt, lastScore, lastIou, this);
    if (dialog.exec() != QDialog::Accepted) {
        return;
    }

    QString promptValue = dialog.getPromptValue();
    double scoreValue = dialog.getScoreValue();
    int iouValue = dialog.getIouValue();

    if (promptValue.isEmpty()) {
        promptValue = "fire";
    }

    lastPrompt = promptValue;
    lastScore = scoreValue;
    lastIou = iouValue;
    saveSettings();

    try {
        // 更新模型参数并重新初始化运行时
        if (videoProcessingCore->get_model_manager()->update_model_params(promptValue.toStdString(), scoreValue, iouValue) &&
            videoProcessingCore->initialize_runtime(inputId.toStdString(), outputId.toStdString())) {

            utils::showScrollableMessageBox(this, tr("成功"),
                tr("AI模型参数已更新并重新初始化!\n提示词: %1\n置信度阈值: %2\nIOU阈值: %3%")
                .arg(promptValue)
                .arg(scoreValue)
                .arg(iouValue),
                QMessageBox::Information);
        }
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("更新模型参数失败: %1").arg(e.what()),
            QMessageBox::Critical);
    }
}

void MainWindow::updateVideoFrame() {
    if (!videoProcessingCore->is_video_opened() || !isPlaying) {
        return;
    }

    cv::Mat frame;
    bool readSuccess = false;

    // 对于RTSP流，添加重试机制
    auto videoProvider = videoProcessingCore->get_video_provider();
    if (videoProvider->is_from_camera() && !videoProvider->get_rtsp_url().empty()) {
        // 尝试读取帧，最多重试3次
        for (int retry = 0; retry < 3; retry++) {
            readSuccess = videoProvider->read_frame(frame);
            if (readSuccess) break;

            // 如果读取失败，等待一小段时间再重试
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    } else {
        // 对于普通视频文件或摄像头，正常读取
        readSuccess = videoProvider->read_frame(frame);
    }

    if (readSuccess) {
        // 更新当前时间显示
        if (!videoProcessingCore->get_video_provider()->is_from_camera()) {
            int currentFrame = videoProcessingCore->get_current_frame();
            double fps = videoProcessingCore->get_fps();
            int totalFrames = videoProcessingCore->get_total_frames();

            int currentSeconds = static_cast<int>(currentFrame / fps);
            int totalSeconds = static_cast<int>(totalFrames / fps);

            currentTimeLabel->setText(tr("%1 / %2")
                .arg(formatTime(currentSeconds))
                .arg(formatTime(totalSeconds)));
        }

        // For file video, update current frame count and slider
        if (!videoProvider->is_from_camera()) {
            // Update slider position
            videoSlider->setValue(videoProcessingCore->get_current_frame());
        }

        // Update frame counter for FPS calculation
        frameCount++;

        // Update display
        updateDisplayFrame(frame);

        // 所有类型的存储都在updateDisplayFrame中处理
        // 因为需要先进行帧处理脚本处理和AI处理
    } else {
        // 读取帧失败
        if (!videoProvider->is_from_camera()) {
            // 视频文件结束
            // 停止播放定时器
            isPlaying = false;
            playTimer->stop();
            fpsTimer->stop();

            if (isRecording) {
                frameRecordingCheckBox->setChecked(false); // This will automatically call stopRecording
            }

            if (isVideoRecording) {
                videoRecordingCheckBox->setChecked(false); // This will automatically call stopVideoRecording
            }

            // 重新加载视频
            loadVideo(videoPathEdit->text());

            // 重置视频到开始位置，但不自动开始播放
            // 注意：在loadVideo中已经设置了初始帧位置，这里不需要再设置
            videoSlider->setValue(0);

            // 确保播放按钮可用并显示正确的文本
            playButton->setEnabled(true);
            playButton->setText(tr("播放"));
        } else {
            // 对于RTSP流，处理连接问题
            if (!videoProvider->get_rtsp_url().empty()) {
                // RTSP流读取失败，显示状态信息
                static QDateTime lastErrorTime = QDateTime::currentDateTime();
                QDateTime currentTime = QDateTime::currentDateTime();

                // 限制错误提示的频率，避免频繁弹窗
                if (lastErrorTime.secsTo(currentTime) >= 10) {
                    statusBar()->showMessage(tr("RTSP流连接不稳定，尝试恢复..."), 5000);
                    lastErrorTime = currentTime;
                }

                // 计数器，连续失败超过30次后尝试重新连接
                static int failCount = 0;
                failCount++;
                if (failCount > 30) {
                    failCount = 0;

                    // 尝试重新连接RTSP流
                    statusBar()->showMessage(tr("正在重新连接RTSP流..."), 5000);

                    // 暂停定时器
                    playTimer->stop();

                    // 重新打开RTSP流
                    if (videoProcessingCore->open_rtsp_stream(videoProvider->get_rtsp_url())) {
                        // 重新启动定时器
                        playTimer->start(1000 / videoProcessingCore->get_fps());
                        statusBar()->showMessage(tr("RTSP流重新连接成功"), 5000);
                    } else {
                        // 重连失败，停止播放
                        stopPlayback();
                        utils::showScrollableMessageBox(this, tr("错误"),
                            tr("RTSP流连接丢失，无法重新连接。"),
                            QMessageBox::Critical);
                    }
                }
            } else {
                // 普通摄像头连接问题
                static int failCount = 0;
                failCount++;
                if (failCount > 10) {
                    stopPlayback();
                    utils::showScrollableMessageBox(this, tr("错误"),
                        tr("摄像头连接丢失，请重新连接。"),
                        QMessageBox::Critical);
                    failCount = 0;
                }
            }
        }
    }
}

void MainWindow::togglePlayback() {
    if (!videoProcessingCore->is_video_opened()) {
        utils::showScrollableMessageBox(this, tr("警告"), tr("请先加载视频文件!"), QMessageBox::Warning);
        return;
    }

    isPlaying = !isPlaying;
    if (isPlaying) {
        // 如果是视频文件（非摄像头）
        auto videoProvider = videoProcessingCore->get_video_provider();
        if (!videoProvider->is_from_camera()) {
            // 检查是否需要重置视频位置
            // 如果当前帧接近视频结尾或者视频已经播放完毕
            int currentFrame = videoProcessingCore->get_current_frame();
            int totalFrames = videoProcessingCore->get_total_frames();

            if (currentFrame >= totalFrames - 5 || currentFrame <= 0) {
                // 重置到视频开头
                videoProvider->set_frame_position(0);
                videoSlider->setValue(0);

                // 读取第一帧并显示，确保视频源已经重置
                cv::Mat firstFrame;
                if (videoProvider->read_frame(firstFrame)) {
                    updateDisplayFrame(firstFrame);
                }
            }
        }

        playButton->setText(tr("暂停")); // 将按钮文本改为"暂停"
        playTimer->start(1000 / videoProcessingCore->get_fps());
        fpsTimer->start(1000); // 每秒更新一次FPS显示
    } else {
        playButton->setText(tr("播放")); // 将按钮文本改回"播放"
        playTimer->stop();
        fpsTimer->stop();
    }
}

void MainWindow::stopPlayback() {
    isPlaying = false;
    playButton->setText(tr("播放")); // 将按钮文本设置为"播放"
    playTimer->stop();
    fpsTimer->stop();

    // 停止帧存储和视频录制
    if (isRecording) {
        frameRecordingCheckBox->setChecked(false); // 这将自动调用stopRecording
    }

    if (isVideoRecording) {
        videoRecordingCheckBox->setChecked(false); // 这将自动调用stopVideoRecording
    }

    if (videoProcessingCore->is_video_opened()) {
        auto videoProvider = videoProcessingCore->get_video_provider();
        videoProvider->set_frame_position(0);
        videoSlider->setValue(0);

        cv::Mat frame;
        if (videoProvider->read_frame(frame)) {
            updateDisplayFrame(frame);
        }
    }
}

void MainWindow::seekVideo(int position) {
    if (!videoProcessingCore->is_video_opened() || videoProcessingCore->get_video_provider()->is_from_camera()) return;

    // 暂停当前播放，如果正在播放
    bool wasPlaying = isPlaying;
    if (isPlaying) {
        playTimer->stop();
    }

    // 设置新的帧位置
    auto videoProvider = videoProcessingCore->get_video_provider();
    videoProvider->set_frame_position(position);

    // 读取并显示当前帧
    cv::Mat frame;
    if (videoProvider->read_frame(frame)) {
        updateDisplayFrame(frame);
    }

    // 更新时间显示
    double fps = videoProcessingCore->get_fps();
    int totalFrames = videoProcessingCore->get_total_frames();
    int currentSeconds = static_cast<int>(position / fps);
    int totalSeconds = static_cast<int>(totalFrames / fps);

    currentTimeLabel->setText(tr("%1 / %2")
        .arg(formatTime(currentSeconds))
        .arg(formatTime(totalSeconds)));

    // 如果之前正在播放，则继续播放
    if (wasPlaying) {
        playTimer->start(1000 / videoProcessingCore->get_fps());
    }
}

void MainWindow::toggleFullscreen() {
    if (!videoProcessingCore->is_video_opened()) {
        utils::showScrollableMessageBox(this, tr("警告"), tr("请先加载视频文件!"), QMessageBox::Warning);
        return;
    }

    if (!fullscreenWindow) {
        fullscreenWindow = new FullscreenVideoWindow(this);
        connect(fullscreenWindow, &FullscreenVideoWindow::destroyed, [this]() {
            fullscreenWindow = nullptr;
        });

        fullscreenWindow->showFullScreen();

        if (!isPlaying && !currentFrameMat.empty()) {
            // 使用新的接口，直接传递帧和处理结果
            if (enableAI) {
                // 如果启用了AI处理，重新处理当前帧获取结果
                ai::FrameResult result = videoProcessingCore->process_frame(currentFrameMat, enableAI);
                fullscreenWindow->updateFrameWithResult(currentFrameMat, result);
            } else {
                // 如果没有启用AI处理，使用兼容接口
                QPixmap currentPixmap = get_current_framePixmap(currentFrameMat);
                fullscreenWindow->updateFrame(currentPixmap);
            }
        }
    } else {
        fullscreenWindow->close();
        fullscreenWindow = nullptr;
    }
}

void MainWindow::toggleRecording(bool checked) {
    if (checked) {
        startRecording();
    } else {
        stopRecording();
    }
}

void MainWindow::toggleVideoRecording(bool checked) {
    if (checked) {
        startVideoRecording();
    } else {
        stopVideoRecording();
    }
}

void MainWindow::setRecordingPath() {
    QString dir = QFileDialog::getExistingDirectory(this, tr("选择帧存储目录"),
        QDir::homePath(), QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);

    if (!dir.isEmpty()) {
        recordingOutputPath = dir;
    }
}

void MainWindow::setVideoRecordingParams() {
    // 创建对话框
    QDialog dialog(this);
    dialog.setWindowTitle(tr("视频录制参数"));
    dialog.setMinimumWidth(400);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // 添加输出视频文件选择
    QHBoxLayout* outputFileLayout = new QHBoxLayout();
    QLabel* outputFileLabel = new QLabel(tr("输出视频文件:"), &dialog);
    QLineEdit* outputFileEdit = new QLineEdit(&dialog);
    outputFileEdit->setReadOnly(true);
    if (!videoOutputPath.isEmpty()) {
        outputFileEdit->setText(videoOutputPath);
    }
    QPushButton* browseFileButton = new QPushButton(tr("浏览..."), &dialog);
    outputFileLayout->addWidget(outputFileLabel);
    outputFileLayout->addWidget(outputFileEdit);
    outputFileLayout->addWidget(browseFileButton);
    layout->addLayout(outputFileLayout);

    // 添加帧率设置
    QHBoxLayout* fpsLayout = new QHBoxLayout();
    QLabel* fpsLabel = new QLabel(tr("帧率(FPS):"), &dialog);
    QDoubleSpinBox* fpsSpinBox = new QDoubleSpinBox(&dialog);
    fpsSpinBox->setRange(1.0, 120.0);
    fpsSpinBox->setValue(videoFps);
    fpsSpinBox->setSingleStep(1.0);
    fpsLayout->addWidget(fpsLabel);
    fpsLayout->addWidget(fpsSpinBox);
    fpsLayout->addStretch();
    layout->addLayout(fpsLayout);

    // 添加编码器选择
    QHBoxLayout* codecLayout = new QHBoxLayout();
    QLabel* codecLabel = new QLabel(tr("编码器:"), &dialog);
    QComboBox* codecComboBox = new QComboBox(&dialog);
    codecComboBox->addItem("H.264", cv::VideoWriter::fourcc('H', '2', '6', '4'));
    codecComboBox->addItem("XVID", cv::VideoWriter::fourcc('X', 'V', 'I', 'D'));
    codecComboBox->addItem("MJPG", cv::VideoWriter::fourcc('M', 'J', 'P', 'G'));
    codecComboBox->addItem("MP4V", cv::VideoWriter::fourcc('M', 'P', '4', 'V'));

    // 设置当前选中的编码器
    int codecIndex = 0;
    for (int i = 0; i < codecComboBox->count(); i++) {
        if (codecComboBox->itemData(i).toInt() == videoCodec) {
            codecIndex = i;
            break;
        }
    }
    codecComboBox->setCurrentIndex(codecIndex);

    codecLayout->addWidget(codecLabel);
    codecLayout->addWidget(codecComboBox);
    codecLayout->addStretch();
    layout->addLayout(codecLayout);

    // 添加按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
    layout->addWidget(buttonBox);

    // 连接信号
    connect(browseFileButton, &QPushButton::clicked, [&]() {
        QString file = QFileDialog::getSaveFileName(&dialog, tr("选择输出视频文件"),
                                                  QDir::homePath(),
                                                  tr("视频文件 (*.mp4 *.avi)"));
        if (!file.isEmpty()) {
            outputFileEdit->setText(file);
        }
    });

    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        videoOutputPath = outputFileEdit->text();
        videoFps = fpsSpinBox->value();
        videoCodec = codecComboBox->currentData().toInt();

        if (videoOutputPath.isEmpty()) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("请选择输出视频文件！"),
                QMessageBox::Warning);
            videoRecordingCheckBox->setChecked(false);
            return;
        }
    } else {
        // 用户取消了对话框
        videoRecordingCheckBox->setChecked(false);
    }
}

void MainWindow::updateFpsDisplay() {
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    qint64 timeElapsed = currentTime - lastFpsUpdateTime;

    if (timeElapsed > 0) {
        double currentFps = frameCount * 1000.0 / timeElapsed;
        currentFpsLabel->setText(tr("当前帧率: %1 FPS").arg(QString::number(currentFps, 'f', 1)));

        frameCount = 0;
        lastFpsUpdateTime = currentTime;
    }
}

void MainWindow::resetCounts() {
    // 重置 AI 处理器中的计数
    videoProcessingCore->get_ai_processor()->reset_counts();
}

void MainWindow::startRecording() {
    if (recordingOutputPath.isEmpty()) {
        setRecordingPath();
        if (recordingOutputPath.isEmpty()) {
            frameRecordingCheckBox->setChecked(false);
            return;
        }
    }

    QDir dir(recordingOutputPath);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("无法创建输出目录!"), QMessageBox::Critical);
            frameRecordingCheckBox->setChecked(false);
            return;
        }
    }

    isRecording = true;
    storedFrameCount = 0;
}

void MainWindow::stopRecording() {
    isRecording = false;
}

void MainWindow::startVideoRecording() {
    // 如果没有设置输出路径，弹出设置对话框
    if (videoOutputPath.isEmpty()) {
        setVideoRecordingParams();
        if (videoOutputPath.isEmpty()) {
            // 在setVideoRecordingParams中已经处理了取消勾选框
            return;
        }
    }

    // 检查是否有视频帧
    if (currentFrameMat.empty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("没有可用的视频帧！请先播放视频。"),
            QMessageBox::Warning);
        videoRecordingCheckBox->setChecked(false);
        return;
    }

    // 创建视频写入器
    bool isOpened = videoWriter.open(videoOutputPath.toStdString(), videoCodec, videoFps, currentFrameMat.size());
    if (!isOpened) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法创建视频文件！\n请检查输出路径和编码器设置。"),
            QMessageBox::Warning);
        videoRecordingCheckBox->setChecked(false);
        return;
    }

    // 开始录制
    isVideoRecording = true;
    statusBar()->showMessage(tr("正在录制视频到: %1").arg(videoOutputPath), 5000);
}

void MainWindow::stopVideoRecording() {
    if (isVideoRecording && videoWriter.isOpened()) {
        // 释放视频写入器
        videoWriter.release();

        // 显示完成消息
        statusBar()->showMessage(tr("视频录制已完成: %1").arg(videoOutputPath), 5000);
    }

    isVideoRecording = false;
}

bool MainWindow::applyRenderingToFrame(cv::Mat& frame, const std::string& ext_info) {
    if (ext_info.empty()) {
        return false;
    }

    try {
        // 解析ext_info中的JSON字符串
        Json::Value ext_info_json;
        Json::CharReaderBuilder builder;
        std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
        std::string errors;

        bool parsing_successful = reader->parse(
            ext_info.c_str(),
            ext_info.c_str() + ext_info.length(),
            &ext_info_json,
            &errors
        );

        if (parsing_successful && ext_info_json.isMember("render_info")) {
            // 使用插件渲染器渲染图像
            if (!pluginRenderer) {
                pluginRenderer = std::make_unique<utils::QPluginRenderer>();
            }

            // 直接使用旧的渲染方法，因为我们需要渲染到图像而不是视图
            return pluginRenderer->render(frame, ext_info_json["render_info"]);
        }
    } catch (const std::exception& e) {
        std::cerr << "Error parsing render_info: " << e.what() << std::endl;
    }

    return false;
}

void MainWindow::updateDisplayFrame(const cv::Mat& frame) {
    if (frame.empty()) return;

    cv::Mat processedFrame;
    ai::FrameResult result;

    // 在非AI模式下，直接使用原始帧
    cv::Mat inputFrame = frame;

    if (enableAI) {
        try {
            result = videoProcessingCore->process_frame(inputFrame, enableAI);

            // 获取处理后的帧
            processedFrame = inputFrame;

            // 如果结果查看器对话框已打开，更新TCP状态
            if (resultViewerDialog && resultViewerDialog->isVisible()) {
                // 更新TCP服务状态
                auto server = videoProcessingCore->get_result_storage_server();
                if (server) {
                    resultViewerDialog->update_tcp_status(
                        server->is_running(),
                        server->get_port(),
                        server->get_client_count()
                    );

                    // 如果自动刷新没有启用，手动加载最新结果
                    resultViewerDialog->load_results_from_server();
                } else {
                    resultViewerDialog->update_tcp_status(false, 0, 0);
                }
            }

            // 如果启用了帧存储，存储处理后的帧
            if (isRecording) {
                // 创建要存储的帧的副本
                cv::Mat frameToSave = processedFrame;

                // 如果需要包含渲染结果，应用渲染
                if (includeRenderingInSavedFrames && !result.ext_info.empty()) {
                    applyRenderingToFrame(frameToSave, result.ext_info);
                }

                QString framePath = QString("%1/frame_%2.png")
                    .arg(recordingOutputPath)
                    .arg(storedFrameCount, 6, 10, QChar('0'));

#ifdef _WIN32
                std::string savePath = utils::utf8ToAnsi(framePath.toStdString());
                cv::imwrite(savePath, frameToSave);
#else
                cv::imwrite(framePath.toStdString(), frameToSave);
#endif
                storedFrameCount++;
            }

            // 如果启用了视频录制，将帧写入视频
            if (isVideoRecording && videoWriter.isOpened()) {
                // 创建要存储的帧的副本
                cv::Mat frameToSave = processedFrame;

                // 如果需要包含渲染结果，应用渲染
                if (includeRenderingInSavedFrames && !result.ext_info.empty()) {
                    applyRenderingToFrame(frameToSave, result.ext_info);
                }

                videoWriter.write(frameToSave);
            }

            // 不再需要应用渲染到显示帧，因为我们使用QPluginRenderer直接渲染到QGraphicsView
        }
        catch (const std::exception& e) {
            static QDateTime lastErrorTime = QDateTime::currentDateTime();
            QDateTime currentTime = QDateTime::currentDateTime();

            // 限制错误提示的频率，避免频繁弹窗
            if (lastErrorTime.secsTo(currentTime) >= 5) {
                // 停止播放
                stopPlayback();

                // 显示错误信息
                utils::showScrollableMessageBox(this,
                    tr("AI处理错误"),
                    tr("AI模型处理失败！\n\n详细信息：%1")
                        .arg(QString::fromStdString(e.what())),
                    QMessageBox::Critical);

                lastErrorTime = currentTime;
            }

            // 发生错误时使用原始帧
            processedFrame = frame;
            return;
        }
    } else {
        // 非AI模式下，直接使用原始帧
        processedFrame = frame;

        // 如果启用了帧存储，存储原始帧
        if (isRecording) {
            QString framePath = QString("%1/frame_%2.png")
                .arg(recordingOutputPath)
                .arg(storedFrameCount, 6, 10, QChar('0'));

#ifdef _WIN32
            std::string savePath = utils::utf8ToAnsi(framePath.toStdString());
            cv::imwrite(savePath, frame);
#else
            cv::imwrite(framePath.toStdString(), frame);
#endif
            storedFrameCount++;
        }

        // 如果启用了视频录制，将帧写入视频
        if (isVideoRecording && videoWriter.isOpened()) {
            videoWriter.write(frame);
        }
    }

    // 更新当前帧
    currentFrameMat = processedFrame;

    // 确保pluginRenderer已初始化
    if (!pluginRenderer) {
        pluginRenderer = std::make_unique<utils::QPluginRenderer>();
    }

    // 检查是否已有场景，如果有则重用
    QGraphicsScene* scene = videoDisplay->scene();
    bool newSceneCreated = false;

    if (!scene) {
        scene = new QGraphicsScene(this);
        newSceneCreated = true;
    } else {
        // 清除现有场景中的所有项目
        scene->clear();
    }

    // 由MainWindow自己负责图像的显示
    QImage image = utils::QPluginRenderer::mat_to_qimage(currentFrameMat);

    if (!image.isNull()) {
        // 添加图像到场景
        QGraphicsPixmapItem* pixmapItem = scene->addPixmap(QPixmap::fromImage(image));
        pixmapItem->setZValue(0); // 设置为背景层

        // 如果是新创建的场景，设置到视图
        if (newSceneCreated) {
            videoDisplay->setScene(scene);
        }

        // 更新场景矩形和视图
        scene->setSceneRect(0, 0, currentFrameMat.cols, currentFrameMat.rows);
        videoDisplay->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);

        // 如果是AI模式，在图像上方渲染图形元素
        if (enableAI) {
            QSize frameSize(currentFrameMat.cols, currentFrameMat.rows);

            // 如果有渲染信息，直接使用
            if (!result.ext_info.empty()) {
                pluginRenderer->render_graphics_frame_result_to_view(videoDisplay, frameSize, result);
            }
            // 如果没有渲染信息但有检测跟踪结果，手动创建基本渲染信息
            else if (!result.detection_tracks.empty()) {
                // 创建一个基本的渲染信息JSON
                Json::Value render_info;
                render_info["should_render"] = true;

                // 添加跟踪目标
                Json::Value tracks_array(Json::arrayValue);
                for (const auto& track : result.detection_tracks) {
                    Json::Value track_obj;
                    track_obj["track_id"] = track.track_id;
                    track_obj["class"] = track.detect_class;
                    track_obj["score"] = track.score;

                    // 添加边界框
                    Json::Value bbox(Json::arrayValue);
                    bbox.append(track.tlwh.x);  // x
                    bbox.append(track.tlwh.y);  // y
                    bbox.append(track.tlwh.width);  // width
                    bbox.append(track.tlwh.height);  // height
                    track_obj["bbox"] = bbox;

                    tracks_array.append(track_obj);
                }
                render_info["tracks"] = tracks_array;

                // 渲染到视图
                pluginRenderer->render_graphics_to_view(videoDisplay, frameSize, render_info);
            }
        }
    } else if (newSceneCreated) {
        // 如果是新创建的场景但图像无效，删除场景
        delete scene;
    }

    // 更新全屏窗口
    if (fullscreenWindow) {
        if (enableAI && (!result.ext_info.empty() || !result.detection_tracks.empty())) {
            // 如果启用了AI处理且有渲染信息或检测跟踪结果，直接传递帧和处理结果
            fullscreenWindow->updateFrameWithResult(currentFrameMat, result);
        } else {
            // 否则使用兼容接口
            QPixmap pixmap = get_current_framePixmap(currentFrameMat);
            fullscreenWindow->updateFrame(pixmap);
        }
    }
}


QPixmap MainWindow::get_current_framePixmap(const cv::Mat& frame) {
    // 使用优化后的mat_to_qimage方法创建QImage
    QImage image = utils::QPluginRenderer::mat_to_qimage(frame);

    // 直接从QImage创建QPixmap，避免额外的内存分配
    return QPixmap::fromImage(image);
}

void MainWindow::testCurrentFrameAI() {
    // 检查是否有当前帧
    if (currentFrameMat.empty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("没有可用的图像帧！\n请先打开视频或图像。"),
            QMessageBox::Warning);
        return;
    }

    // 检查模型是否已加载
    if (!videoProcessingCore->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    // 检查节点ID是否已设置
    QString inputNodeId = inputNodeIdEdit->text();
    QString outputNodeId = outputNodeIdEdit->text();
    if (inputNodeId.isEmpty() || outputNodeId.isEmpty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先设置输入和输出节点ID！"),
            QMessageBox::Warning);
        return;
    }

    // 暂停播放
    bool wasPlaying = isPlaying;
    if (isPlaying) {
        stopPlayback();
    }

    try {
        // 显示处理中对话框
        QProgressDialog progress(tr("正在进行AI处理测试..."), tr("取消"), 0, 0, this);
        progress.setWindowModality(Qt::WindowModal);
        progress.setMinimumDuration(0);
        progress.show();
        QApplication::processEvents();

        // 测试AI处理
        // 使用原始帧进行AI处理
        ai::FrameResult result = videoProcessingCore->get_ai_processor()->process_with_ai(
            currentFrameMat,
            inputNodeId.toStdString(),
            outputNodeId.toStdString()
        );

        // 获取处理后的帧
        cv::Mat processedFrame = currentFrameMat;

        // 使用QPluginRenderer直接渲染到QGraphicsView
        if (!pluginRenderer) {
            pluginRenderer = std::make_unique<utils::QPluginRenderer>();
        }

        // 检查是否已有场景，如果有则重用
        QGraphicsScene* scene = videoDisplay->scene();
        bool newSceneCreated = false;

        if (!scene) {
            scene = new QGraphicsScene(this);
            newSceneCreated = true;
        } else {
            // 清除现有场景中的所有项目
            scene->clear();
        }

        // 由MainWindow自己负责图像的显示
        QImage image = utils::QPluginRenderer::mat_to_qimage(processedFrame);

        if (!image.isNull()) {
            // 添加图像到场景
            QGraphicsPixmapItem* pixmapItem = scene->addPixmap(QPixmap::fromImage(image));
            pixmapItem->setZValue(0); // 设置为背景层

            // 如果是新创建的场景，设置到视图
            if (newSceneCreated) {
                videoDisplay->setScene(scene);
            }

            // 更新场景矩形和视图
            scene->setSceneRect(0, 0, processedFrame.cols, processedFrame.rows);
            videoDisplay->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);

            // 如果有渲染信息，在图像上方渲染图形元素
            if (!result.ext_info.empty()) {
                QSize frameSize(processedFrame.cols, processedFrame.rows);
                pluginRenderer->render_graphics_frame_result_to_view(videoDisplay, frameSize, result);
            }
        } else if (newSceneCreated) {
            // 如果是新创建的场景但图像无效，删除场景
            delete scene;
        }

        // 如果结果查看器对话框已打开，更新TCP状态
        if (resultViewerDialog && resultViewerDialog->isVisible()) {
            // 更新TCP服务状态
            auto server = videoProcessingCore->get_result_storage_server();
            if (server) {
                resultViewerDialog->update_tcp_status(
                    server->is_running(),
                    server->get_port(),
                    server->get_client_count()
                );

                // 如果自动刷新没有启用，手动加载最新结果
                resultViewerDialog->load_results_from_server();
            } else {
                resultViewerDialog->update_tcp_status(false, 0, 0);
            }
        }

        // 显示成功消息
        utils::showScrollableMessageBox(this, tr("测试成功"),
            tr("AI模型测试成功！\n\n") +
            tr("输入节点ID: %1\n").arg(inputNodeId) +
            tr("输出节点ID: %2\n").arg(outputNodeId) +
            (enableCounting ? tr("\n已启用目标计数模式") : tr("\n普通AI处理模式")),
            QMessageBox::Information);

        // 如果之前在播放，恢复播放
        if (wasPlaying) {
            togglePlayback();
        }
    }
    catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("测试失败"),
            tr("AI模型测试失败！\n\n详细信息：%1")
                .arg(QString::fromStdString(e.what())),
            QMessageBox::Critical);
    }
    catch (...) {
        utils::showScrollableMessageBox(this, tr("测试失败"),
            tr("AI模型测试时发生未知错误！"),
            QMessageBox::Critical);
    }
}
bool MainWindow::setupPluginManagement() {
    // 创建工具栏并设置为垂直方向
    QToolBar* toolBar = new QToolBar(tr("工具栏"), this);
    toolBar->setObjectName("mainToolBar");
    addToolBar(Qt::LeftToolBarArea, toolBar);  // 添加到左侧
    toolBar->setOrientation(Qt::Vertical);     // 设置为垂直方向

    // 设置工具栏属性
    toolBar->setMovable(false);  // 禁止移动
    toolBar->setIconSize(QSize(32, 32));  // 适当加大图标尺寸
    toolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);

    // === 文件操作组 ===
    // 添加分隔线和标签
    QLabel* fileLabel = new QLabel(tr(" 文件操作 "));
    fileLabel->setAlignment(Qt::AlignCenter);
    toolBar->addWidget(fileLabel);

    // 创建文件操作动作
    QAction* openFileAction = new QAction(tr("打开视频"), this);
    openFileAction->setIcon(QIcon(":/rc/icons/file_icon.png"));
    toolBar->addAction(openFileAction);
    connect(openFileAction, &QAction::triggered, this, &MainWindow::browseVideoFile);

    // 添加分隔线
    toolBar->addSeparator();

    // === 播放控制组 ===
    QLabel* playControlLabel = new QLabel(tr(" 播放控制 "));
    playControlLabel->setAlignment(Qt::AlignCenter);
    toolBar->addWidget(playControlLabel);

    // 创建播放控制动作
    QAction* playAction = new QAction(tr("播放"), this);
    playAction->setIcon(QIcon(":/rc/icons/play_icon.png"));
    toolBar->addAction(playAction);
    connect(playAction, &QAction::triggered, this, &MainWindow::togglePlayback);

    QAction* stopAction = new QAction(tr("停止"), this);
    stopAction->setIcon(QIcon(":/rc/icons/stop_icon.png"));
    toolBar->addAction(stopAction);
    connect(stopAction, &QAction::triggered, this, &MainWindow::stopPlayback);

    // 添加分隔线
    toolBar->addSeparator();

    // === 任务管理组 ===
    QLabel* taskLabel = new QLabel(tr(" 任务管理 "));
    taskLabel->setAlignment(Qt::AlignCenter);
    toolBar->addWidget(taskLabel);

    // 创建任务管理动作
    QAction* taskManagerAction = new QAction(tr("任务管理"), this);
    taskManagerAction->setIcon(QIcon(":/rc/icons/task_icon.png"));
    toolBar->addAction(taskManagerAction);
    connect(taskManagerAction, &QAction::triggered, this, &MainWindow::openTaskManager);

    // 添加分隔线
    toolBar->addSeparator();

    // === 系统设置组 ===
    QLabel* settingsLabel = new QLabel(tr(" 系统设置 "));
    settingsLabel->setAlignment(Qt::AlignCenter);
    toolBar->addWidget(settingsLabel);

    // 创建设置动作
    QAction* settingsAction = new QAction(tr("设置"), this);
    settingsAction->setIcon(QIcon(":/rc/icons/settings_icon.png"));
    toolBar->addAction(settingsAction);

    return true;  // 返回设置是否成功
}

void MainWindow::updateNodeIds() {
    // 获取输入输出节点ID
    QString inputId = inputNodeIdEdit->text();
    QString outputId = outputNodeIdEdit->text();

    // 检查输入输出节点ID是否为空
    if (inputId.isEmpty() || outputId.isEmpty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("输入和输出节点ID不能为空！"),
            QMessageBox::Warning);
        return;
    }

    // 检查模型是否已加载
    if (!videoProcessingCore->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    // 显示处理中对话框
    QProgressDialog progress(tr("正在应用节点设置..."), tr("取消"), 0, 0, this);
    progress.setWindowModality(Qt::WindowModal);
    progress.setMinimumDuration(0);
    progress.show();
    QApplication::processEvents();

    try {
        // 更新运行时的输入输出节点ID
        if (videoProcessingCore->initialize_runtime(inputId.toStdString(), outputId.toStdString())) {
            // 关闭进度对话框
            progress.close();

            // 更新成功，显示成功消息
            utils::showScrollableMessageBox(this, tr("成功"),
                tr("输入输出节点ID已更新为：\n输入：%1\n输出：%2")
                .arg(inputId)
                .arg(outputId),
                QMessageBox::Information);

            // 在状态栏也显示成功信息
            statusBar()->showMessage(tr("输入输出节点ID已更新"), 3000);
        }
    } catch (const std::exception& e) {
        // 关闭进度对话框
        progress.close();

        // 更新失败，显示错误信息
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("更新输入输出节点ID失败: %1").arg(e.what()),
            QMessageBox::Warning);
    }
}

QString MainWindow::formatTime(int seconds) {
    int hours = seconds / 3600;
    int minutes = (seconds % 3600) / 60;
    int secs = seconds % 60;
    return QString("%1:%2:%3")
        .arg(hours, 2, 10, QChar('0'))
        .arg(minutes, 2, 10, QChar('0'))
        .arg(secs, 2, 10, QChar('0'));
}

bool MainWindow::ensureTaskPluginsLoaded() {
    // 如果插件已经加载，直接返回成功
    if (taskPluginsLoaded) {
        return true;
    }

    // 创建 plugins/task 目录（如果不存在）
    QDir pluginsDir(QCoreApplication::applicationDirPath() + "/plugins/task");
    if (!pluginsDir.exists()) {
        pluginsDir.mkpath(".");
        qDebug() << "Created plugins/task directory";
    }

    // 确保插件管理器已初始化
    if (!pluginManager) {
        pluginManager = std::make_shared<ai::plugins::PluginManager>("plugins");
    }

    // 设置插件加载回调
    pluginManager->set_plugin_load_callback([this](const std::string& plugin_path, bool success) {
        if (success) {
            qDebug() << "Plugin loaded successfully:" << QString::fromStdString(plugin_path);
        } else {
            qDebug() << "Plugin failed to load:" << QString::fromStdString(plugin_path);
            // 在状态栏显示错误信息
            statusBar()->showMessage(tr("插件加载失败: %1").arg(QString::fromStdString(plugin_path)), 5000);
        }
    });

    // 设置插件目录
    std::string plugin_dir = "plugins/task";
    pluginManager->set_plugin_path(plugin_dir);

    // 加载目录中的所有插件
    int loaded_count = pluginManager->load_plugins_from_directory(plugin_dir);

    // 获取所有插件
    auto plugins = pluginManager->get_all_plugins();

    // 注册所有插件到VideoProcessingCore并启用它们
    for (const auto& plugin : plugins) {
        std::string pluginName = plugin->get_name();

        // 启用插件
        pluginManager->enable_plugin(pluginName);

        // 将插件注册到VideoProcessingCore
        if (videoProcessingCore) {
            videoProcessingCore->register_plugin(plugin);
        }
    }

    // 在状态栏显示成功信息
    statusBar()->showMessage(tr("已加载 %1 个插件").arg(loaded_count), 5000);

    qDebug() << "Loaded" << loaded_count << "plugins from" << QString::fromStdString(plugin_dir);

    // 标记插件已加载
    taskPluginsLoaded = true;

    return loaded_count > 0;
}

void MainWindow::autoLoadTaskPlugins() {
    // 调用确保插件加载的方法
    ensureTaskPluginsLoaded();
}

void MainWindow::launchVideoStreamSystem() {
    // 获取当前程序所在目录
    QString exePath = QCoreApplication::applicationDirPath();
    QString aivideovsPath = exePath + "/AiVideoVS.exe";

    // 检查文件是否存在
    if (!QFile::exists(aivideovsPath)) {
        QMessageBox::critical(this, tr("错误"), tr("找不到AiVideoVS.exe，请确保该文件存在于程序目录中。"));
        return;
    }

    // 使用QProcess启动AiVideoVS.exe
    QProcess process;
    process.setProgram(aivideovsPath);
    process.startDetached();

    // 由于startDetached是异步的，我们无法立即知道是否成功启动
    // 但我们可以检查文件是否存在，这已经在前面完成了

    // 显示成功消息
    statusBar()->showMessage(tr("已启动多路视频流处理系统"), 3000);
}

void MainWindow::launchSafetyAI() {
    // 获取当前程序所在目录
    QString exePath = QCoreApplication::applicationDirPath();
    QString safetyAIPath = exePath + "/SafetyAI.exe";

    // 检查文件是否存在
    if (!QFile::exists(safetyAIPath)) {
        QMessageBox::critical(this, tr("错误"), tr("找不到SafetyAI.exe，请确保该文件存在于程序目录中。"));
        return;
    }

    // 使用QProcess启动SafetyAI.exe
    QProcess process;
    process.setProgram(safetyAIPath);
    process.startDetached();

    // 由于startDetached是异步的，我们无法立即知道是否成功启动
    // 但我们可以检查文件是否存在，这已经在前面完成了

    // 显示成功消息
    statusBar()->showMessage(tr("已启动算法平台"), 3000);
}

}
