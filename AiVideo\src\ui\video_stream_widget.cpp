#include "ui/video_stream_widget.h"
#include "ui/fullscreen_window.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QFileDialog>
#include <QMessageBox>
#include <QInputDialog>
#include <QApplication>
#include <QProgressDialog>
#include <QDateTime>
#include <QResizeEvent>
#include <iostream>

#include "utils/dialog_utils.h"
#include "utils/string_utils.h"

namespace ui {

VideoStreamWidget::VideoStreamWidget(QWidget* parent)
    : QWidget(parent),
      streamName(tr("视频流")),
      isPlaying(false),
      frameCount(0),
      lastFpsUpdateTime(0),
      enableAI(false),
      frameSkipInterval(1),
      fullscreenWindow(nullptr)
{
    // 初始化UI
    initializeUI();

    // 初始化视频处理核心
    videoProcessingCore = std::make_shared<core::VideoProcessingCore>();

    // 初始化定时器
    playTimer = new QTimer(this);
    connect(playTimer, &QTimer::timeout, this, &VideoStreamWidget::updateVideoFrame);

    // 初始化FPS定时器
    fpsTimer = new QTimer(this);
    fpsTimer->setInterval(1000); // 每秒更新一次
    connect(fpsTimer, &QTimer::timeout, this, &VideoStreamWidget::updateFpsDisplay);
}

VideoStreamWidget::~VideoStreamWidget()
{
    // 停止播放
    stopPlayback();

    // 关闭全屏窗口
    if (fullscreenWindow) {
        fullscreenWindow->close();
        delete fullscreenWindow;
        fullscreenWindow = nullptr;
    }
}

void VideoStreamWidget::initializeUI()
{
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);

    // 创建标题和关闭按钮布局
    QHBoxLayout* titleLayout = new QHBoxLayout();
    QLabel* titleLabel = new QLabel(streamName, this);
    titleLabel->setStyleSheet("font-weight: bold;");

    closeButton = new QPushButton(tr("关闭"), this);
    closeButton->setFixedWidth(60);
    connect(closeButton, &QPushButton::clicked, [this]() {
        emit closeStreamRequested(this);
    });

    titleLayout->addWidget(titleLabel);
    titleLayout->addStretch();
    titleLayout->addWidget(closeButton);

    mainLayout->addLayout(titleLayout);

    // 创建视频源设置组
    QGroupBox* sourceGroup = new QGroupBox(tr("视频源"), this);
    QVBoxLayout* sourceLayout = new QVBoxLayout(sourceGroup);

    // 视频路径设置
    QHBoxLayout* videoPathLayout = new QHBoxLayout();
    QLabel* videoPathLabel = new QLabel(tr("视频文件:"), this);
    videoPathEdit = new QLineEdit(this);
    videoPathEdit->setReadOnly(true);
    QPushButton* browseVideoBtn = new QPushButton(tr("浏览"), this);
    connect(browseVideoBtn, &QPushButton::clicked, this, &VideoStreamWidget::browseVideoFile);

    videoPathLayout->addWidget(videoPathLabel);
    videoPathLayout->addWidget(videoPathEdit);
    videoPathLayout->addWidget(browseVideoBtn);

    // 模型路径设置
    QHBoxLayout* modelPathLayout = new QHBoxLayout();
    QLabel* modelPathLabel = new QLabel(tr("AI模型:"), this);
    modelPathEdit = new QLineEdit(this);
    modelPathEdit->setReadOnly(true);
    QPushButton* browseLocalModelBtn = new QPushButton(tr("加载本地模型"), this);
    connect(browseLocalModelBtn, &QPushButton::clicked, this, &VideoStreamWidget::browseLocalModelFile);
    QPushButton* browseNetworkModelBtn = new QPushButton(tr("加载网络模型"), this);
    connect(browseNetworkModelBtn, &QPushButton::clicked, this, &VideoStreamWidget::browseNetworkModelFile);

    modelPathLayout->addWidget(modelPathLabel);
    modelPathLayout->addWidget(modelPathEdit);
    modelPathLayout->addWidget(browseLocalModelBtn);
    modelPathLayout->addWidget(browseNetworkModelBtn);

    // 节点ID设置
    QHBoxLayout* nodeIdLayout = new QHBoxLayout();
    QLabel* inputNodeLabel = new QLabel(tr("输入节点:"), this);
    inputNodeIdEdit = new QLineEdit("image", this);
    QLabel* outputNodeLabel = new QLabel(tr("输出节点:"), this);
    outputNodeIdEdit = new QLineEdit("检测/pred", this);

    nodeIdLayout->addWidget(inputNodeLabel);
    nodeIdLayout->addWidget(inputNodeIdEdit);
    nodeIdLayout->addWidget(outputNodeLabel);
    nodeIdLayout->addWidget(outputNodeIdEdit);

    sourceLayout->addLayout(videoPathLayout);
    sourceLayout->addLayout(modelPathLayout);
    sourceLayout->addLayout(nodeIdLayout);

    mainLayout->addWidget(sourceGroup);

    // 创建视频显示区域
    QGroupBox* displayGroup = new QGroupBox(tr("视频预览"), this);
    QVBoxLayout* displayLayout = new QVBoxLayout(displayGroup);

    videoDisplay = new QLabel(this);
    videoDisplay->setMinimumSize(320, 240);
    videoDisplay->setAlignment(Qt::AlignCenter);
    videoDisplay->setText(tr("加载视频后将在此处显示"));
    videoDisplay->setStyleSheet("background-color: #222222; color: white;");

    // 创建控制按钮布局
    QHBoxLayout* controlLayout = new QHBoxLayout();

    playButton = new QPushButton(tr("播放"), this);
    stopButton = new QPushButton(tr("停止"), this);
    fullscreenButton = new QPushButton(tr("全屏"), this);

    connect(playButton, &QPushButton::clicked, this, &VideoStreamWidget::togglePlayback);
    connect(stopButton, &QPushButton::clicked, this, &VideoStreamWidget::stopPlayback);
    connect(fullscreenButton, &QPushButton::clicked, this, &VideoStreamWidget::toggleFullscreen);

    controlLayout->addWidget(playButton);
    controlLayout->addWidget(stopButton);
    controlLayout->addWidget(fullscreenButton);
    controlLayout->addStretch();

    // 创建时间和进度条布局
    QHBoxLayout* timeLayout = new QHBoxLayout();
    currentTimeLabel = new QLabel(tr("00:00:00 / 00:00:00"), this);
    timeLayout->addWidget(currentTimeLabel);

    // 进度条
    videoSlider = new QSlider(Qt::Horizontal, this);
    videoSlider->setEnabled(false);
    connect(videoSlider, &QSlider::sliderMoved, this, &VideoStreamWidget::seekVideo);

    // 创建FPS显示布局
    QHBoxLayout* fpsLayout = new QHBoxLayout();
    originalFpsLabel = new QLabel(tr("原始帧率: - FPS"), this);
    currentFpsLabel = new QLabel(tr("当前帧率: - FPS"), this);
    fpsLayout->addWidget(originalFpsLabel);
    fpsLayout->addWidget(currentFpsLabel);
    fpsLayout->addStretch();

    // 创建处理模式选择
    QHBoxLayout* processingLayout = new QHBoxLayout();
    noProcessingRadio = new QRadioButton(tr("原始视频"), this);
    aiProcessingRadio = new QRadioButton(tr("AI处理"), this);

    processingModeGroup = new QButtonGroup(this);
    processingModeGroup->addButton(noProcessingRadio, 0);
    processingModeGroup->addButton(aiProcessingRadio, 1);
    noProcessingRadio->setChecked(true);

    connect(processingModeGroup, static_cast<void(QButtonGroup::*)(int)>(&QButtonGroup::idClicked),
            [this](int id) {
                enableAI = (id == 1);

                // 如果启用AI但模型未加载，提示用户
                if (enableAI && !videoProcessingCore->is_model_loaded()) {
                    utils::showScrollableMessageBox(this, tr("警告"), tr("请先加载AI模型文件!"));
                }

                // 更新当前帧显示
                if (videoProcessingCore->is_video_opened() && !currentFrameMat.empty()) {
                    updateDisplayFrame(currentFrameMat);
                }
            });

    processingLayout->addWidget(noProcessingRadio);
    processingLayout->addWidget(aiProcessingRadio);
    processingLayout->addStretch();

    // AI设置组
    aiSettingsGroup = new QGroupBox(tr("AI设置"), this);
    QVBoxLayout* aiSettingsLayout = new QVBoxLayout(aiSettingsGroup);

    // 这里可以添加更多AI设置控件

    aiSettingsGroup->setEnabled(false);

    // 将所有布局添加到显示组
    displayLayout->addWidget(videoDisplay);
    displayLayout->addLayout(controlLayout);
    displayLayout->addWidget(videoSlider);
    displayLayout->addLayout(timeLayout);
    displayLayout->addLayout(fpsLayout);
    displayLayout->addLayout(processingLayout);
    displayLayout->addWidget(aiSettingsGroup);

    mainLayout->addWidget(displayGroup);

    // 设置边框和样式
    setStyleSheet("QGroupBox { border: 1px solid #cccccc; border-radius: 5px; margin-top: 1ex; } "
                  "QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; }");
}

std::shared_ptr<core::VideoProcessingCore> VideoStreamWidget::getVideoProcessingCore() const
{
    return videoProcessingCore;
}

void VideoStreamWidget::setVideoPath(const QString& path)
{
    videoPathEdit->setText(path);
}

QString VideoStreamWidget::getVideoPath() const
{
    return videoPathEdit->text();
}

void VideoStreamWidget::setModelPath(const QString& path)
{
    modelPathEdit->setText(path);
}

QString VideoStreamWidget::getModelPath() const
{
    return modelPathEdit->text();
}

void VideoStreamWidget::setStreamName(const QString& name)
{
    streamName = name;
    // 更新标题
    QList<QLabel*> labels = findChildren<QLabel*>();
    for (QLabel* label : labels) {
        if (label->text() == streamName) {
            label->setText(name);
            break;
        }
    }
}

QString VideoStreamWidget::getStreamName() const
{
    return streamName;
}

void VideoStreamWidget::enableAIProcessingMode(bool enable)
{
    if (enable) {
        aiProcessingRadio->setChecked(true);
    } else {
        noProcessingRadio->setChecked(true);
    }
    enableAI = enable;
    aiSettingsGroup->setEnabled(enable);
}

void VideoStreamWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);

    // 如果有当前帧，更新显示以适应新尺寸
    if (!currentFrameMat.empty()) {
        updateDisplayFrame(currentFrameMat);
    }
}

void VideoStreamWidget::browseVideoFile()
{
    try {
        // 打开文件对话框选择视频文件
        QString filePath = QFileDialog::getOpenFileName(
            this,
            tr("选择视频文件"),
            QDir::homePath(),
            tr("视频文件 (*.mp4 *.avi *.mkv *.mov);;所有文件 (*.*)"));

        if (!filePath.isEmpty()) {
            // 显示加载中对话框
            QProgressDialog progress(tr("正在加载视频..."), tr("取消"), 0, 0, this);
            progress.setWindowModality(Qt::WindowModal);
            progress.setMinimumDuration(0);
            progress.show();
            QApplication::processEvents();

            // 加载视频文件
            loadVideo(filePath);

            // 关闭进度对话框
            progress.close();
        }
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("打开视频文件时发生异常: %1").arg(e.what()),
            QMessageBox::Critical);
    } catch (...) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("打开视频文件时发生未知异常"),
            QMessageBox::Critical);
    }
}

void VideoStreamWidget::browseLocalModelFile()
{
    // 打开文件对话框选择模型文件
    QString filePath = QFileDialog::getOpenFileName(
        this,
        tr("选择AI模型文件"),
        QDir::homePath(),
        tr("模型文件 (*.vfmodel *.onnx);;所有文件 (*.*)"));

    if (!filePath.isEmpty()) {
        // 加载模型文件
        loadModel(filePath);
    }
}

void VideoStreamWidget::browseNetworkModelFile()
{
    QString url = QInputDialog::getText(this, tr("输入模型URL"), tr("请输入模型URL:"));
    if (!url.isEmpty()) {
        // 加载模型文件
        loadModel(url);
    }
}

void VideoStreamWidget::loadVideo(const QString& filePath)
{
    try {
        // 确保文件存在
        if (!QFile::exists(filePath)) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("视频文件不存在: %1").arg(filePath),
                QMessageBox::Critical);
            return;
        }

        // 停止当前播放
        stopPlayback();

        // 确保视频处理核心已初始化
        if (!videoProcessingCore) {
            videoProcessingCore = std::make_shared<core::VideoProcessingCore>();
            if (!videoProcessingCore) {
                utils::showScrollableMessageBox(this, tr("错误"),
                    tr("无法初始化视频处理核心"),
                    QMessageBox::Critical);
                return;
            }
        }

        // 清空历史信息缓存
        if (videoProcessingCore) {
            // 重置AI处理器中的计数和跟踪器状态
            auto aiProcessor = videoProcessingCore->get_ai_processor();
            if (aiProcessor) {
                std::cout << "重置AI处理器状态和计数信息" << std::endl;
                aiProcessor->reset_counts();

                // 清空帧内存中的历史帧和跟踪结果
                auto frameMemory = aiProcessor->get_frame_memory();
                if (frameMemory) {
                    std::cout << "清空帧内存历史缓存" << std::endl;
                    frameMemory->clear();
                }
            }

            // 如果结果存储服务器正在运行，停止并重新启动它
            auto resultServer = videoProcessingCore->get_result_storage_server();
            if (resultServer && resultServer->is_running()) {
                std::cout << "重启结果存储服务器以清空历史结果" << std::endl;
                int tcpPort = resultServer->get_port();
                auto storageMode = resultServer->get_storage_mode();
                int flushInterval = resultServer->get_flush_interval();
                std::string storagePath = resultServer->get_storage_path();

                // 停止当前服务器
                videoProcessingCore->stop_result_storage_server();

                // 重新启动服务器（这将创建新的存储）
                videoProcessingCore->start_result_storage_server(storagePath, storageMode, tcpPort, flushInterval);
            }
        }

        // 关闭之前打开的视频
        videoProcessingCore->close_video();

        // 打开新的视频文件
        std::cout << "Opening video file: " << filePath.toStdString() << std::endl;
        if (!videoProcessingCore->open_video_file(filePath.toStdString())) {
            utils::showScrollableMessageBox(this, tr("错误"), tr("无法打开视频文件！"), QMessageBox::Critical);
            return;
        }

        // 设置播放器滑块范围
        int totalFrames = videoProcessingCore->get_total_frames();
        if (totalFrames <= 0) {
            utils::showScrollableMessageBox(this, tr("警告"),
                tr("视频帧数为0，可能无法正常播放"),
                QMessageBox::Warning);
        }

        videoSlider->setRange(0, totalFrames > 0 ? totalFrames - 1 : 0);
        videoSlider->setValue(0);
        videoSlider->setEnabled(totalFrames > 0);

        // 读取第一帧显示
        cv::Mat frame;
        if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
            if (frame.empty()) {
                std::cout << "First frame is empty" << std::endl;
            } else {
                std::cout << "First frame read successfully: " << frame.cols << "x" << frame.rows << std::endl;
                updateDisplayFrame(frame);
            }
        } else {
            std::cout << "Failed to read first frame" << std::endl;
            utils::showScrollableMessageBox(this, tr("警告"),
                tr("无法读取视频第一帧，可能无法正常播放"),
                QMessageBox::Warning);
        }

        // 更新视频路径显示
        videoPathEdit->setText(filePath);

        // 更新原始帧率显示
        double fps = videoProcessingCore->get_fps();
        if (fps <= 0) fps = 25.0; // 如果无法获取帧率，使用默认值
        originalFpsLabel->setText(tr("原始帧率: %1 FPS").arg(fps, 0, 'f', 1));

        // 更新时间显示
        int totalSeconds = totalFrames > 0 ? static_cast<int>(totalFrames / fps) : 0;
        currentTimeLabel->setText(tr("00:00:00 / %1").arg(formatTime(totalSeconds)));

        // 设置AI处理器的帧率
        if (videoProcessingCore->get_ai_processor()) {
            videoProcessingCore->get_ai_processor()->set_fps(fps);
        }

        std::cout << "Video loaded successfully and history cache cleared: " << filePath.toStdString() << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Exception in loadVideo: " << e.what() << std::endl;
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("加载视频时发生异常: %1").arg(e.what()),
            QMessageBox::Critical);
    } catch (...) {
        std::cerr << "Unknown exception in loadVideo" << std::endl;
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("加载视频时发生未知异常"),
            QMessageBox::Critical);
    }
}

void VideoStreamWidget::loadModel(const QString& filePath)
{
    // 检查输入输出节点ID是否为空
    QString inputId = inputNodeIdEdit->text();
    QString outputId = outputNodeIdEdit->text();

    if (inputId.isEmpty() || outputId.isEmpty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("输入和输出节点ID不能为空！"),
            QMessageBox::Warning);
        return;
    }

    // 显示加载中对话框
    QProgressDialog progress(tr("正在加载AI模型..."), tr("取消"), 0, 0, this);
    progress.setWindowModality(Qt::WindowModal);
    progress.setMinimumDuration(0);
    progress.show();
    QApplication::processEvents();

    // 加载模型
    bool success = videoProcessingCore->load_model(filePath.toStdString());

    // 关闭进度对话框
    progress.close();

    if (!success) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法加载AI模型！请确认模型文件格式正确。"),
            QMessageBox::Critical);
        return;
    }

    // 初始化运行时
    if (!videoProcessingCore->initialize_runtime(inputId.toStdString(), outputId.toStdString())) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法初始化模型运行时！请检查输入输出节点ID是否正确。"),
            QMessageBox::Critical);
        return;
    }

    // 更新模型路径显示
    modelPathEdit->setText(filePath);

    // 如果当前已选择AI处理模式，更新显示
    if (enableAI && videoProcessingCore->is_video_opened() && !currentFrameMat.empty()) {
        updateDisplayFrame(currentFrameMat);
    }

    // 显示成功消息
    utils::showScrollableMessageBox(this, tr("成功"),
        tr("模型加载成功！"),
        QMessageBox::Information);
}

// void VideoStreamWidget::openCamera(int cameraId)
// {
//     // 停止当前播放
//     stopPlayback();

//     // 打开摄像头
//     if (!videoProcessingCore->open_camera(cameraId)) {
//         utils::showScrollableMessageBox(this, tr("错误"),
//             tr("无法打开摄像头 ID %1！请确认摄像头连接正常。").arg(cameraId),
//             QMessageBox::Critical);
//         return;
//     }

//     // 由于摄像头没有总帧数，禁用进度条
//     videoSlider->setEnabled(false);

//     // 读取第一帧显示
//     cv::Mat frame;
//     if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
//         updateDisplayFrame(frame);
//     }

//     // 更新视频路径显示
//     videoPathEdit->setText(tr("摄像头 ID: %1").arg(cameraId));

//     // 更新原始帧率显示
//     double fps = videoProcessingCore->get_fps();
//     originalFpsLabel->setText(tr("原始帧率: %1 FPS").arg(fps, 0, 'f', 1));

//     // 更新时间显示
//     currentTimeLabel->setText(tr("00:00:00 / 实时流"));

//     // 自动开始播放
//     if (!isPlaying) {
//         togglePlayback();
//     }
// }

void VideoStreamWidget::openRtspStream(const QString& url)
{
    // 停止当前播放
    stopPlayback();

    // 显示连接中对话框
    QProgressDialog progress(tr("正在连接RTSP流..."), tr("取消"), 0, 0, this);
    progress.setWindowModality(Qt::WindowModal);
    progress.setMinimumDuration(0);
    progress.show();
    QApplication::processEvents();

    // 打开RTSP流
    if (!videoProcessingCore->open_rtsp_stream(url.toStdString())) {
        progress.close();
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法打开RTSP流！\n请确认URL正确且流媒体服务器可访问。"),
            QMessageBox::Critical);
        return;
    }

    progress.close();

    // 由于流没有总帧数，禁用进度条
    videoSlider->setEnabled(false);

    // 读取第一帧显示
    cv::Mat frame;
    if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
        updateDisplayFrame(frame);
    }

    // 更新视频路径显示
    videoPathEdit->setText(url);

    // 更新原始帧率显示
    double fps = videoProcessingCore->get_fps();
    originalFpsLabel->setText(tr("原始帧率: %1 FPS").arg(fps, 0, 'f', 1));

    // 更新时间显示
    currentTimeLabel->setText(tr("00:00:00 / 实时流"));

    // 自动开始播放
    if (!isPlaying) {
        togglePlayback();
    }
}

void VideoStreamWidget::togglePlayback()
{
    if (!videoProcessingCore->is_video_opened()) {
        utils::showScrollableMessageBox(this, tr("警告"), tr("请先加载视频文件!"), QMessageBox::Warning);
        return;
    }

    isPlaying = !isPlaying;
    if (isPlaying) {
        // 如果是视频文件（非摄像头）
        auto videoProvider = videoProcessingCore->get_video_provider();
        if (!videoProvider->is_from_camera()) {
            // 检查是否需要重置视频位置
            // 如果当前帧接近视频结尾或者视频已经播放完毕
            int currentFrame = videoProcessingCore->get_current_frame();
            int totalFrames = videoProcessingCore->get_total_frames();

            if (currentFrame >= totalFrames - 5 || currentFrame <= 0) {
                // 重置到视频开头
                videoProvider->set_frame_position(0);
                videoSlider->setValue(0);

                // 读取第一帧并显示，确保视频源已经重置
                cv::Mat firstFrame;
                if (videoProvider->read_frame(firstFrame)) {
                    updateDisplayFrame(firstFrame);
                }
            }
        }

        playButton->setText(tr("暂停")); // 将按钮文本改为"暂停"
        playTimer->start(1000 / videoProcessingCore->get_fps());
        fpsTimer->start(1000); // 每秒更新一次FPS显示
    } else {
        playButton->setText(tr("播放")); // 将按钮文本改回"播放"
        playTimer->stop();
        fpsTimer->stop();
    }
}

void VideoStreamWidget::stopPlayback()
{
    try {
        isPlaying = false;
        playButton->setText(tr("播放")); // 将按钮文本设置为"播放"

        // 停止定时器
        if (playTimer->isActive()) {
            playTimer->stop();
        }

        if (fpsTimer->isActive()) {
            fpsTimer->stop();
        }

        // 重置视频位置
        if (videoProcessingCore && videoProcessingCore->is_video_opened()) {
            auto videoProvider = videoProcessingCore->get_video_provider();
            if (videoProvider) {
                try {
                    // 只有对非摄像头源才重置位置
                    if (!videoProvider->is_from_camera()) {
                        videoProvider->set_frame_position(0);
                        videoSlider->setValue(0);

                        cv::Mat frame;
                        if (videoProvider->read_frame(frame) && !frame.empty()) {
                            updateDisplayFrame(frame);
                        } else {
                            std::cout << "Warning: Failed to read first frame after stopping playback" << std::endl;
                        }
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Exception while resetting video position: " << e.what() << std::endl;
                }
            } else {
                std::cerr << "Warning: Video provider is null in stopPlayback" << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception in stopPlayback: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception in stopPlayback" << std::endl;
    }
}

void VideoStreamWidget::seekVideo(int position)
{
    if (!videoProcessingCore->is_video_opened() || videoProcessingCore->get_video_provider()->is_from_camera()) {
        return;
    }

    // 设置视频位置
    videoProcessingCore->get_video_provider()->set_frame_position(position);

    // 读取并显示当前帧
    cv::Mat frame;
    if (videoProcessingCore->get_video_provider()->read_frame(frame)) {
        updateDisplayFrame(frame);
    }

    // 更新时间显示
    double fps = videoProcessingCore->get_fps();
    if (fps > 0) {
        int currentSeconds = static_cast<int>(position / fps);
        int totalSeconds = static_cast<int>(videoProcessingCore->get_total_frames() / fps);
        currentTimeLabel->setText(tr("%1 / %2").arg(formatTime(currentSeconds)).arg(formatTime(totalSeconds)));
    }
}

void VideoStreamWidget::toggleFullscreen()
{
    if (!fullscreenWindow) {
        fullscreenWindow = new FullscreenVideoWindow();
        connect(fullscreenWindow, &FullscreenVideoWindow::destroyed, [this]() {
            fullscreenWindow = nullptr;
        });
    }

    if (!currentFrameMat.empty()) {
        QPixmap pixmap = getCurrentFramePixmap(currentFrameMat);
        fullscreenWindow->updateFrame(pixmap);
    }

    fullscreenWindow->showFullScreen();
}

void VideoStreamWidget::updateVideoFrame()
{
    try {
        // 检查视频是否打开和是否正在播放
        if (!videoProcessingCore || !videoProcessingCore->is_video_opened() || !isPlaying) {
            return;
        }

        cv::Mat frame;
        bool readSuccess = false;

        // 获取视频提供者
        auto videoProvider = videoProcessingCore->get_video_provider();
        if (!videoProvider) {
            std::cerr << "Error: Video provider is null" << std::endl;
            return;
        }

        // 对于RTSP流，添加重试机制
        if (videoProvider->is_from_camera() && !videoProvider->get_rtsp_url().empty()) {
            // 尝试读取帧，最多重试3次
            for (int retry = 0; retry < 3; retry++) {
                try {
                    readSuccess = videoProvider->read_frame(frame);
                    if (readSuccess && !frame.empty()) break;

                    // 如果读取失败，等待一小段时间再重试
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                } catch (const std::exception& e) {
                    std::cerr << "Exception while reading RTSP frame (attempt " << retry + 1 << "): " << e.what() << std::endl;
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
            }
        } else {
            // 对于普通视频文件或摄像头，正常读取
            try {
                readSuccess = videoProvider->read_frame(frame);
            } catch (const std::exception& e) {
                std::cerr << "Exception while reading frame: " << e.what() << std::endl;
                readSuccess = false;
            }
        }

        if (readSuccess && !frame.empty()) {
            // 更新进度条（如果是视频文件）
            if (!videoProvider->is_from_camera()) {
                int currentFrame = videoProcessingCore->get_current_frame();
                int totalFrames = videoProcessingCore->get_total_frames();

                if (currentFrame >= 0 && currentFrame < totalFrames) {
                    videoSlider->setValue(currentFrame);

                    // 更新时间显示
                    double fps = videoProcessingCore->get_fps();
                    if (fps > 0) {
                        int currentSeconds = static_cast<int>(currentFrame / fps);
                        int totalSeconds = static_cast<int>(totalFrames / fps);
                        currentTimeLabel->setText(tr("%1 / %2").arg(formatTime(currentSeconds)).arg(formatTime(totalSeconds)));
                    }
                }
            }

            // 更新帧计数器，用于计算FPS
            frameCount++;

            // 处理帧
            updateDisplayFrame(frame);
        } else {
            // 读取帧失败
            if (!videoProvider->is_from_camera()) {
                // 视频文件结束
                std::cout << "End of video file reached" << std::endl;

                // 停止播放定时器
                isPlaying = false;
                playTimer->stop();
                fpsTimer->stop();
                playButton->setText(tr("播放"));

                // 重新加载视频
                QString videoPath = videoPathEdit->text();
                if (!videoPath.isEmpty() && QFile::exists(videoPath)) {
                    try {
                        loadVideo(videoPath);
                    } catch (const std::exception& e) {
                        std::cerr << "Exception while reloading video: " << e.what() << std::endl;
                    }
                }
            } else {
                // 对于RTSP流或摄像头，处理连接丢失
                if (!videoProvider->get_rtsp_url().empty()) {
                    // RTSP流连接问题
                    static int failCount = 0;
                    failCount++;
                    if (failCount > 30) {
                        failCount = 0;
                        std::cout << "RTSP connection lost, attempting to reconnect" << std::endl;

                        // 尝试重新连接RTSP流
                        QString rtspUrl = videoPathEdit->text();

                        // 暂停定时器
                        playTimer->stop();

                        try {
                            // 重新打开RTSP流
                            if (videoProcessingCore->open_rtsp_stream(rtspUrl.toStdString())) {
                                // 重新启动定时器
                                double fps = videoProcessingCore->get_fps();
                                if (fps <= 0) fps = 25.0; // 默认值
                                playTimer->start(1000 / fps);
                                std::cout << "RTSP reconnection successful" << std::endl;
                            } else {
                                // 重连失败，停止播放
                                stopPlayback();
                                utils::showScrollableMessageBox(this, tr("错误"),
                                    tr("RTSP流连接丢失，无法重新连接。"),
                                    QMessageBox::Critical);
                            }
                        } catch (const std::exception& e) {
                            std::cerr << "Exception during RTSP reconnection: " << e.what() << std::endl;
                            stopPlayback();
                            utils::showScrollableMessageBox(this, tr("错误"),
                                tr("RTSP流重连异常: %1").arg(e.what()),
                                QMessageBox::Critical);
                        }
                    }
                } else {
                    // 普通摄像头连接问题
                    static int failCount = 0;
                    failCount++;
                    if (failCount > 10) {
                        std::cout << "Camera connection lost" << std::endl;
                        stopPlayback();
                        utils::showScrollableMessageBox(this, tr("错误"),
                            tr("摄像头连接丢失，请重新连接。"),
                            QMessageBox::Critical);
                        failCount = 0;
                    }
                }
            }
        }
    } catch (const cv::Exception& e) {
        std::cerr << "OpenCV exception in updateVideoFrame: " << e.what() << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Exception in updateVideoFrame: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception in updateVideoFrame" << std::endl;
    }
}

void VideoStreamWidget::updateFpsDisplay()
{
    // 计算当前帧率
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    qint64 elapsed = currentTime - lastFpsUpdateTime;

    if (elapsed > 0) {
        double currentFps = frameCount * 1000.0 / elapsed;
        currentFpsLabel->setText(tr("当前帧率: %1 FPS").arg(currentFps, 0, 'f', 1));
    }

    // 重置计数器
    frameCount = 0;
    lastFpsUpdateTime = currentTime;
}

QString VideoStreamWidget::formatTime(int seconds)
{
    int h = seconds / 3600;
    int m = (seconds % 3600) / 60;
    int s = seconds % 60;
    return QString("%1:%2:%3")
        .arg(h, 2, 10, QChar('0'))
        .arg(m, 2, 10, QChar('0'))
        .arg(s, 2, 10, QChar('0'));
}

QPixmap VideoStreamWidget::getCurrentFramePixmap(const cv::Mat& frame)
{
    try {
        // 检查帧是否为空
        if (frame.empty()) {
            std::cout << "Warning: Empty frame passed to getCurrentFramePixmap" << std::endl;
            return QPixmap();
        }

        // 检查帧尺寸
        if (frame.cols <= 0 || frame.rows <= 0) {
            std::cout << "Warning: Invalid frame dimensions: " << frame.cols << "x" << frame.rows << std::endl;
            return QPixmap();
        }

        // 将OpenCV的Mat转换为QImage
        QImage img;
        if (frame.channels() == 3) {
            // 彩色图像
            try {
                cv::Mat rgbFrame;
                cv::cvtColor(frame, rgbFrame, cv::COLOR_BGR2RGB);
                if (rgbFrame.empty()) {
                    std::cout << "Warning: Color conversion resulted in empty frame" << std::endl;
                    return QPixmap();
                }
                img = QImage(rgbFrame.data, rgbFrame.cols, rgbFrame.rows, rgbFrame.step, QImage::Format_RGB888);
            } catch (const cv::Exception& e) {
                std::cerr << "OpenCV exception during color conversion: " << e.what() << std::endl;
                return QPixmap();
            }
        } else if (frame.channels() == 1) {
            // 灰度图像
            img = QImage(frame.data, frame.cols, frame.rows, frame.step, QImage::Format_Grayscale8);
        } else {
            // 不支持的格式
            std::cout << "Warning: Unsupported number of channels: " << frame.channels() << std::endl;
            return QPixmap();
        }

        // 检查QImage是否有效
        if (img.isNull()) {
            std::cout << "Warning: Failed to create QImage from frame" << std::endl;
            return QPixmap();
        }

        return QPixmap::fromImage(img);
    } catch (const cv::Exception& e) {
        std::cerr << "OpenCV exception in getCurrentFramePixmap: " << e.what() << std::endl;
        return QPixmap();
    } catch (const std::exception& e) {
        std::cerr << "Exception in getCurrentFramePixmap: " << e.what() << std::endl;
        return QPixmap();
    } catch (...) {
        std::cerr << "Unknown exception in getCurrentFramePixmap" << std::endl;
        return QPixmap();
    }
}

void VideoStreamWidget::updateDisplayFrame(const cv::Mat& frame)
{
    try {
        if (frame.empty()) {
            std::cout << "Warning: Empty frame passed to updateDisplayFrame" << std::endl;
            return;
        }
        // 在非AI模式下，直接使用原始帧
        cv::Mat inputFrame;
        ai::FrameResult result;
        try {
            inputFrame = frame.clone();
            if (inputFrame.empty()) {
                std::cout << "Warning: Failed to clone input frame" << std::endl;
                return;
            }
        } catch (const cv::Exception& e) {
            std::cerr << "OpenCV exception while cloning frame: " << e.what() << std::endl;
            return;
        } catch (const std::exception& e) {
            std::cerr << "Exception while cloning frame: " << e.what() << std::endl;
            return;
        }

        if (enableAI) {
            try {
                // 确保视频处理核心已初始化
                if (!videoProcessingCore) {
                    std::cerr << "Error: videoProcessingCore is null" << std::endl;
                } else {
                    // 使用AI处理帧
                    result = videoProcessingCore->process_frame(inputFrame, enableAI);
                }
            } catch (const cv::Exception& e) {
                std::cerr << "OpenCV exception during AI processing: " << e.what() << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "Exception during AI processing: " << e.what() << std::endl;
            } catch (...) {
                std::cerr << "Unknown exception during AI processing" << std::endl;
            }
        } else {
        }

        // 更新当前帧
        currentFrameMat = inputFrame;

        // 显示帧
        QPixmap pixmap = getCurrentFramePixmap(currentFrameMat);
        if (pixmap.isNull()) {
            std::cout << "Warning: Failed to convert frame to QPixmap" << std::endl;
            return;
        }

        videoDisplay->setPixmap(pixmap.scaled(videoDisplay->size(),
                                            Qt::KeepAspectRatio,
                                            Qt::SmoothTransformation));

        // 如果全屏窗口存在，更新全屏显示
        if (fullscreenWindow && fullscreenWindow->isVisible()) {
            fullscreenWindow->updateFrame(pixmap);
        }
    } catch (const cv::Exception& e) {
        std::cerr << "OpenCV exception in updateDisplayFrame: " << e.what() << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Exception in updateDisplayFrame: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception in updateDisplayFrame" << std::endl;
    }
}

} // namespace ui
