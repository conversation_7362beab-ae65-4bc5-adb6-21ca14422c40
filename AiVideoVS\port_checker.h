#ifndef PORT_CHECKER_H
#define PORT_CHECKER_H

#include <string>
#include <vector>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>
#include <QDebug>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "iphlpapi.lib")

/**
 * @brief 端口检查工具类，用于检查端口是否可用和防火墙状态
 */
class PortChecker {
public:
    /**
     * @brief 检查端口是否被占用
     * @param port 要检查的端口号
     * @return 如果端口被占用返回true，否则返回false
     */
    static bool isPortInUse(int port) {
        // 初始化Winsock
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            qWarning() << "WSAStartup failed";
            return true; // 假设端口被占用
        }

        // 创建套接字
        SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (sock == INVALID_SOCKET) {
            qWarning() << "Socket creation failed";
            WSACleanup();
            return true; // 假设端口被占用
        }

        // 设置地址结构
        sockaddr_in addr;
        addr.sin_family = AF_INET;
        addr.sin_port = htons(port);
        addr.sin_addr.s_addr = INADDR_ANY;

        // 尝试绑定端口
        int result = bind(sock, (sockaddr*)&addr, sizeof(addr));
        
        // 关闭套接字
        closesocket(sock);
        WSACleanup();

        // 如果绑定失败，端口可能被占用
        return (result == SOCKET_ERROR);
    }

    /**
     * @brief 获取当前正在使用的TCP端口列表
     * @return 端口列表
     */
    static std::vector<int> getInUseTcpPorts() {
        std::vector<int> ports;
        
        // 获取TCP表
        PMIB_TCPTABLE pTcpTable = NULL;
        DWORD dwSize = 0;
        DWORD dwRetVal = 0;

        // 第一次调用获取需要的缓冲区大小
        dwRetVal = GetTcpTable(NULL, &dwSize, TRUE);
        if (dwRetVal == ERROR_INSUFFICIENT_BUFFER) {
            pTcpTable = (PMIB_TCPTABLE)malloc(dwSize);
            if (pTcpTable == NULL) {
                qWarning() << "Memory allocation failed for TCP table";
                return ports;
            }
        } else {
            qWarning() << "GetTcpTable failed with error: " << dwRetVal;
            return ports;
        }

        // 获取TCP表
        dwRetVal = GetTcpTable(pTcpTable, &dwSize, TRUE);
        if (dwRetVal == NO_ERROR) {
            // 遍历表中的每个条目
            for (DWORD i = 0; i < pTcpTable->dwNumEntries; i++) {
                // 获取本地端口
                int localPort = ntohs((u_short)pTcpTable->table[i].dwLocalPort);
                ports.push_back(localPort);
            }
        } else {
            qWarning() << "GetTcpTable failed with error: " << dwRetVal;
        }

        // 释放内存
        if (pTcpTable != NULL) {
            free(pTcpTable);
            pTcpTable = NULL;
        }

        return ports;
    }

    /**
     * @brief 检查端口是否被防火墙阻止
     * @param port 要检查的端口号
     * @return 如果端口被防火墙阻止返回true，否则返回false
     * @note 此方法只是一个简单的检查，不能保证100%准确
     */
    static bool isPortBlockedByFirewall(int port) {
        // 这个方法需要管理员权限才能准确检查
        // 这里只是一个简化的实现，实际上需要使用Windows防火墙API
        
        // 如果端口不在使用中，但无法连接，可能是被防火墙阻止
        if (!isPortInUse(port)) {
            // 尝试创建一个临时服务器并接受连接
            WSADATA wsaData;
            if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
                return true; // 假设被阻止
            }

            SOCKET listenSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
            if (listenSocket == INVALID_SOCKET) {
                WSACleanup();
                return true; // 假设被阻止
            }

            sockaddr_in addr;
            addr.sin_family = AF_INET;
            addr.sin_port = htons(port);
            addr.sin_addr.s_addr = INADDR_ANY;

            if (bind(listenSocket, (sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
                closesocket(listenSocket);
                WSACleanup();
                return true; // 假设被阻止
            }

            if (listen(listenSocket, SOMAXCONN) == SOCKET_ERROR) {
                closesocket(listenSocket);
                WSACleanup();
                return true; // 假设被阻止
            }

            // 设置为非阻塞模式
            u_long mode = 1;
            ioctlsocket(listenSocket, FIONBIO, &mode);

            // 等待一小段时间看是否有连接
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(listenSocket, &readSet);

            timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;

            int result = select(0, &readSet, NULL, NULL, &timeout);

            closesocket(listenSocket);
            WSACleanup();

            // 如果没有连接，可能是被防火墙阻止
            return (result == 0);
        }

        return false; // 端口已被使用，不考虑防火墙
    }

    /**
     * @brief 查找可用的端口
     * @param startPort 起始端口号
     * @param endPort 结束端口号
     * @return 找到的第一个可用端口，如果没有找到返回-1
     */
    static int findAvailablePort(int startPort = 8888, int endPort = 9999) {
        for (int port = startPort; port <= endPort; port++) {
            if (!isPortInUse(port)) {
                return port;
            }
        }
        return -1; // 没有找到可用端口
    }
};

#endif // PORT_CHECKER_H


