/*
 * AiVideoVS 全局样式表
 * 高科技主题风格
 */

/* 全局样式 - 设置默认字体 */
* {
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    color: #dfe6e9;
    background-color: #15202b;
}

/* 主窗口样式 */
QMainWindow {
    border: 1px solid #38444d;
    background: #15202b;
}

/* 菜单栏样式 */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #192734,
                               stop:1 #15202b);
    color: #dfe6e9;
    border-bottom: 1px solid #38444d;
    padding: 4px;
}

QMenuBar::item {
    background: transparent;
    padding: 6px 10px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background: rgba(0, 168, 255, 0.2);
}

QMenuBar::item:pressed {
    background: rgba(0, 168, 255, 0.3);
}

/* 菜单样式 */
QMenu {
    background-color: #192734;
    border: 1px solid #38444d;
    border-radius: 6px;
    padding: 5px 0px;
}

QMenu::item {
    padding: 8px 25px 8px 20px;
}

QMenu::item:selected {
    background: rgba(0, 168, 255, 0.2);
}

QMenu::separator {
    height: 1px;
    background-color: #38444d;
    margin: 5px 0px;
}

/* 按钮样式 */
QPushButton {
    background-color: #2980b9;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 80px;
    outline: none;
    font-size: 9pt;
}

QPushButton:hover {
    background-color: #3498db;
}

QPushButton:pressed {
    background-color: #1a5c8a;
}

QPushButton:disabled {
    background-color: #192734;
    color: #38444d;
}

/* 组框样式 */
QGroupBox {
    border: 1px solid #38444d;
    border-radius: 8px;
    margin-top: 15px;
    font-weight: bold;
    padding: 10px;
    background: rgba(25, 39, 52, 0.8);
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 0px 5px;
    color: #0abde3;
}

/* 标签样式 */
QLabel {
    color: #dfe6e9;
}

/* 表格样式 */
QTableWidget {
    border: 1px solid #38444d;
    border-radius: 6px;
    background-color: #192734;
    gridline-color: #38444d;
    selection-background-color: rgba(0, 168, 255, 0.2);
    selection-color: #dfe6e9;
}

QTableWidget::item {
    padding: 5px;
    border-radius: 4px;
}

QTableWidget::item:selected {
    background-color: rgba(0, 168, 255, 0.2);
}

QHeaderView::section {
    background-color: #15202b;
    color: #0abde3;
    padding: 5px;
    border: none;
    border-right: 1px solid #38444d;
    border-bottom: 1px solid #38444d;
}

/* 下拉框样式 */
QComboBox {
    border: 1px solid #38444d;
    border-radius: 6px;
    padding: 5px 10px;
    background-color: #192734;
    selection-background-color: rgba(0, 168, 255, 0.2);
    selection-color: #dfe6e9;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(:/icons/down-arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #38444d;
    border-radius: 6px;
    background-color: #192734;
    selection-background-color: rgba(0, 168, 255, 0.2);
}

/* 视频流控件样式 */
VideoStreamWidget {
    border: 1px solid #38444d;
    border-radius: 8px;
    background-color: #192734;
    padding: 5px;
}

VideoStreamWidget QLabel {
    color: #dfe6e9;
}

VideoStreamWidget QLabel#videoLabel {
    background-color: #10171e;
    border-radius: 4px;
    border: 1px solid #38444d;
}

/* 状态栏样式 */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #15202b,
                               stop:1 #192734);
    color: #dfe6e9;
    border-top: 1px solid #38444d;
}

/* 滚动条样式 */
QScrollBar:vertical {
    border: none;
    background: #10171e;
    width: 8px;
    margin: 0px;
    border-radius: 4px;
}

QScrollBar::handle:vertical {
    background: #00a8ff;
    border-radius: 4px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #0abde3;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: #10171e;
    height: 8px;
    margin: 0px;
    border-radius: 4px;
}

QScrollBar::handle:horizontal {
    background: #00a8ff;
    border-radius: 4px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background: #0abde3;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 进度对话框样式 */
QProgressDialog {
    background-color: #192734;
    border: 1px solid #38444d;
    border-radius: 8px;
}

QProgressBar {
    border: 1px solid #38444d;
    border-radius: 6px;
    background-color: #15202b;
    text-align: center;
    color: #dfe6e9;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                               stop:0 #00a8ff,
                               stop:1 #00d2d3);
    border-radius: 5px;
}

/* 工具提示样式 */
QToolTip {
    background-color: #192734;
    color: #dfe6e9;
    border: 1px solid #38444d;
    border-radius: 4px;
    padding: 5px;
}

/* 窗口控制按钮样式 */
#minimizeButton, #maximizeButton, #restoreButton, #closeButton {
    background: transparent;
    border: none;
    border-radius: 0;
    min-width: 40px;
    max-width: 40px;
    min-height: 28px;
    max-height: 28px;
    padding: 0;
    margin: 0;
    font-family: "Segoe UI", "Microsoft YaHei";
    font-size: 10pt;
    color: #dfe6e9;
}

#minimizeButton:hover, #maximizeButton:hover, #restoreButton:hover {
    background: rgba(255, 255, 255, 0.1);
}

#minimizeButton:pressed, #maximizeButton:pressed, #restoreButton:pressed {
    background: rgba(255, 255, 255, 0.2);
}

#closeButton:hover {
    background: rgba(255, 0, 0, 0.7);
}

#closeButton:pressed {
    background: rgb(255, 0, 0);
}

/* 标题栏样式 */
QWidget#titleBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #192734,
                               stop:1 #15202b);
    min-height: 36px;
    max-height: 36px;
    border-bottom: 1px solid #38444d;
}

#titleBar QLabel {
    color: #dfe6e9;
    font-size: 10pt;
    font-weight: bold;
}

/* 内容容器样式 */
QWidget#contentWidget {
    background-color: #15202b;
    border: none;
    padding: 5px;
}

/* 配置按钮样式 */
QPushButton#configButton {
    background-color: #2c3e50;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 10pt;
    font-weight: bold;
    min-width: 130px;
    min-height: 32px;
}

QPushButton#configButton:hover {
    background-color: #34495e;
}

QPushButton#configButton:pressed {
    background-color: #1c2e40;
}

/* 表格按钮样式 */
QPushButton#tableButton {
    background-color: #2c3e50;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 4px 6px;
    font-size: 9pt;
    font-weight: bold;
    min-height: 26px;
}

QPushButton#tableButton:hover {
    background-color: #34495e;
}

QPushButton#tableButton:pressed {
    background-color: #1c2e40;
}

/* 表格单元格按钮样式 */
QPushButton#cellButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 3px 5px;
    font-size: 9pt;
    font-weight: bold;
    min-height: 26px;
    min-width: 70px;
}

QPushButton#cellButton:hover {
    background-color: #41a9ed;
}

QPushButton#cellButton:pressed {
    background-color: #2980b9;
}

/* 可折叠区域样式 */
QWidget#sectionTitleBar {
    background-color: #192734;
    border: 1px solid #38444d;
    border-radius: 3px;
    padding: 1px 5px;
    margin-bottom: 0px;
    min-height: 20px;
    max-height: 20px;
}

QWidget#sectionTitleBar:hover {
    background-color: #1e3246;
}

QLabel#sectionTitle {
    color: #0abde3;
    font-weight: bold;
    font-size: 8pt;
}

QPushButton#toggleButton {
    background-color: transparent;
    border: none;
    min-width: 12px;
    max-width: 12px;
    min-height: 12px;
    max-height: 12px;
    padding: 0;
    margin: 0;
}

QWidget#configContent, QWidget#controlContent {
    background-color: #15202b;
    border: 1px solid #38444d;
    border-top: none;
    border-radius: 0px 0px 3px 3px;
    padding: 5px;
    margin-top: -1px;
}

/* 折叠状态下的容器样式 */
QWidget#configContainer[collapsed="true"], QWidget#controlContainer[collapsed="true"] {
    min-height: 22px;
    max-height: 22px;
}

QWidget#configContainer, QWidget#controlContainer {
    margin-bottom: 2px;
}
