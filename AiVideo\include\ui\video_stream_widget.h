#pragma once

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QTimer>
#include <QLineEdit>
#include <QCheckBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QGroupBox>
#include <memory>

#include "core/video_processing_core.h"
#include "ui/fullscreen_window.h"

namespace ui {

/**
 * @brief 视频流显示控件，封装单个视频流的显示和控制
 */
class VideoStreamWidget : public QWidget {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    VideoStreamWidget(QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~VideoStreamWidget();

    /**
     * @brief 获取视频处理核心
     * @return 视频处理核心指针
     */
    std::shared_ptr<core::VideoProcessingCore> getVideoProcessingCore() const;

    /**
     * @brief 设置视频路径
     * @param path 视频路径
     */
    void setVideoPath(const QString& path);

    /**
     * @brief 获取视频路径
     * @return 视频路径
     */
    QString getVideoPath() const;

    /**
     * @brief 设置模型路径
     * @param path 模型路径
     */
    void setModelPath(const QString& path);

    /**
     * @brief 获取模型路径
     * @return 模型路径
     */
    QString getModelPath() const;

    /**
     * @brief 设置流名称
     * @param name 流名称
     */
    void setStreamName(const QString& name);

    /**
     * @brief 获取流名称
     * @return 流名称
     */
    QString getStreamName() const;

    /**
     * @brief 启用AI处理模式
     * @param enable 是否启用
     */
    void enableAIProcessingMode(bool enable);

    /**
     * @brief 加载视频
     * @param filePath 视频文件路径
     */
    void loadVideo(const QString& filePath);

    /**
     * @brief 加载模型
     * @param filePath 模型文件路径
     */
    void loadModel(const QString& filePath);

    /**
     * @brief 打开摄像头
     * @param cameraId 摄像头ID
     */
    void openCamera(int cameraId);

    /**
     * @brief 打开RTSP流
     * @param url RTSP URL
     */
    void openRtspStream(const QString& url);

public slots:
    /**
     * @brief 浏览视频文件
     */
    void browseVideoFile();

    /**
     * @brief 浏览模型文件
     */
    void browseModelFile();

    /**
     * @brief 切换播放/暂停
     */
    void togglePlayback();

    /**
     * @brief 停止播放
     */
    void stopPlayback();

    /**
     * @brief 更新视频帧
     */
    void updateVideoFrame();

    /**
     * @brief 跳转到指定帧
     * @param position 帧位置
     */
    void seekVideo(int position);

    /**
     * @brief 切换全屏显示
     */
    void toggleFullscreen();

    /**
     * @brief 更新FPS显示
     */
    void updateFpsDisplay();

signals:
    /**
     * @brief 请求关闭流的信号
     * @param widget 请求关闭的控件
     */
    void closeStreamRequested(VideoStreamWidget* widget);

protected:
    /**
     * @brief 窗口大小变化事件处理
     * @param event 大小变化事件
     */
    void resizeEvent(QResizeEvent* event) override;

private:
    /**
     * @brief 初始化UI
     */
    void initializeUI();

    /**
     * @brief 更新显示帧
     * @param frame 要显示的帧
     */
    void updateDisplayFrame(const cv::Mat& frame);

    /**
     * @brief 获取当前帧的QPixmap
     * @param frame 当前帧
     * @return 帧的QPixmap
     */
    QPixmap getCurrentFramePixmap(const cv::Mat& frame);

    /**
     * @brief 格式化时间
     * @param seconds 秒数
     * @return 格式化后的时间字符串
     */
    QString formatTime(int seconds);

private:
    // 流名称
    QString streamName;

    // UI组件
    QLineEdit* videoPathEdit;
    QLineEdit* modelPathEdit;
    QLineEdit* inputNodeIdEdit;
    QLineEdit* outputNodeIdEdit;
    QLabel* currentTimeLabel;
    QLabel* videoDisplay;
    QPushButton* playButton;
    QPushButton* stopButton;
    QSlider* videoSlider;
    QPushButton* closeButton;
    QPushButton* fullscreenButton;
    QRadioButton* noProcessingRadio;
    QRadioButton* aiProcessingRadio;
    QButtonGroup* processingModeGroup;
    QGroupBox* aiSettingsGroup;
    QLabel* originalFpsLabel;
    QLabel* currentFpsLabel;

    // 视频播放相关
    QTimer* playTimer;
    QTimer* fpsTimer;
    bool isPlaying;
    int frameCount;
    qint64 lastFpsUpdateTime;

    // AI处理相关
    bool enableAI;
    int frameSkipInterval;

    // 全屏相关
    FullscreenVideoWindow* fullscreenWindow;

    // 存储当前帧以便窗口大小变化时使用
    cv::Mat currentFrameMat;

    // 核心组件
    std::shared_ptr<core::VideoProcessingCore> videoProcessingCore;
};

} // namespace ui
