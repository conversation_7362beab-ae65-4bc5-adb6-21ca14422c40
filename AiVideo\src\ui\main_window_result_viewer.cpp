#include <QMessageBox>
#include <QInputDialog>
#include <QSpinBox>
#include <QTableWidget>
#include <QHeaderView>
#include <QDialogButtonBox>
#include <QFormLayout>
#include <QGroupBox>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QRadioButton>
#include <QComboBox>
#include <QPushButton>
#include <iostream>

#include "ui/main_window.h"
#include "utils/dialog_utils.h"

namespace ui {
void MainWindow::openResultViewer() {
    std::cout << "Opening result viewer dialog" << std::endl;

    // 如果结果查看器对话框不存在，创建一个
    if (!resultViewerDialog) {
        std::cout << "Creating new result viewer dialog" << std::endl;
        resultViewerDialog = std::make_unique<ResultViewerDialog>(this);

        // 设置对话框为非模态，这样用户可以继续操作主窗口
        resultViewerDialog->setModal(false);
    }

    // 检查结果存储服务是否已启动
    auto server = videoProcessingCore->get_result_storage_server();
    if (!server || !server->is_running()) {
        std::cout << "Result storage server not found or not running, starting one..." << std::endl;

        // 如果结果存储服务未启动，使用当前的结果存储设置自动启动一个
        bool success = videoProcessingCore->start_result_storage_server(
            "results",
            resultStorageMode,
            resultStorageTcpPort,
            resultStorageFlushInterval,
            resultStorageProtocolType
        );

        if (success) {
            std::cout << "Result storage server started successfully" << std::endl;
            server = videoProcessingCore->get_result_storage_server();
            enableResultStorage = true;

            // 如果是Modbus协议，设置寄存器映射
            if (resultStorageProtocolType == core::protocols::ProtocolType::MODBUS) {
                videoProcessingCore->set_modbus_register_map(modbusRegisterMap);
            }

            // 保存设置
            saveSettings();
        } else {
            std::cout << "Failed to start result storage server" << std::endl;
        }
    } else {
        std::cout << "Result storage server already running" << std::endl;
    }

    // 设置结果存储服务器
    resultViewerDialog->set_result_storage_server(server);

    // 更新服务状态
    if (server) {
        bool running = server->is_running();
        int port = server->get_port();
        int clients = server->get_client_count();
        auto protocol_type = server->get_protocol_type();

        std::cout << "Updating service status: running=" << running
                  << ", port=" << port
                  << ", clients=" << clients
                  << ", protocol=" << static_cast<int>(protocol_type) << std::endl;

        resultViewerDialog->update_tcp_status(running, port, clients, protocol_type);

        // 启用自动刷新功能，确保实时显示结果
        resultViewerDialog->enable_auto_refresh(true);
    } else {
        std::cout << "No server available, setting service status to inactive" << std::endl;
        resultViewerDialog->update_tcp_status(false, 0, 0);
    }

    // 从存储服务器加载结果
    if (server && server->is_running()) {
        resultViewerDialog->load_results_from_server();
    }

    // 显示对话框
    resultViewerDialog->show();
    resultViewerDialog->raise();
    resultViewerDialog->activateWindow();
}

void MainWindow::toggleResultStorage(bool checked) {
    enableResultStorage = checked;

    if (enableResultStorage) {
        // 创建结果存储设置对话框
        QDialog dialog(this);
        dialog.setWindowTitle(tr("结果存储设置"));
        dialog.setMinimumWidth(500);
        dialog.setMinimumHeight(600);

        QVBoxLayout *layout = new QVBoxLayout(&dialog);

        // 协议类型选择
        QGroupBox *protocolGroupBox = new QGroupBox(tr("协议类型"), &dialog);
        QVBoxLayout *protocolLayout = new QVBoxLayout(protocolGroupBox);

        QComboBox *protocolComboBox = new QComboBox(protocolGroupBox);
        protocolComboBox->addItem(tr("TCP"), static_cast<int>(core::protocols::ProtocolType::TCP));
        protocolComboBox->addItem(tr("Modbus"), static_cast<int>(core::protocols::ProtocolType::MODBUS));
        protocolComboBox->addItem(tr("MQTT"), static_cast<int>(core::protocols::ProtocolType::MQTT));
        protocolComboBox->addItem(tr("自定义"), static_cast<int>(core::protocols::ProtocolType::CUSTOM));

        // 设置当前选中的协议类型
        int currentProtocolIndex = protocolComboBox->findData(static_cast<int>(resultStorageProtocolType));
        if (currentProtocolIndex >= 0) {
            protocolComboBox->setCurrentIndex(currentProtocolIndex);
        }

        protocolLayout->addWidget(protocolComboBox);
        layout->addWidget(protocolGroupBox);

        // 存储路径设置
        QGroupBox *pathGroupBox = new QGroupBox(tr("存储路径"), &dialog);
        QHBoxLayout *pathLayout = new QHBoxLayout(pathGroupBox);

        QLineEdit *pathEdit = new QLineEdit(pathGroupBox);
        pathEdit->setText("results"); // 默认路径
        pathEdit->setPlaceholderText(tr("输入存储路径，如: results 或 D:/my_results"));

        QPushButton *browseButton = new QPushButton(tr("浏览..."), pathGroupBox);
        connect(browseButton, &QPushButton::clicked, [&pathEdit, &dialog]() {
            QString dir = QFileDialog::getExistingDirectory(&dialog,
                tr("选择存储目录"), pathEdit->text());
            if (!dir.isEmpty()) {
                pathEdit->setText(dir);
            }
        });

        pathLayout->addWidget(pathEdit);
        pathLayout->addWidget(browseButton);
        layout->addWidget(pathGroupBox);

        // 存储模式选择
        QGroupBox *modeGroupBox = new QGroupBox(tr("存储模式"), &dialog);
        QVBoxLayout *modeLayout = new QVBoxLayout(modeGroupBox);

        QRadioButton *immediateRadio = new QRadioButton(tr("即时存储"), modeGroupBox);
        QRadioButton *scheduledRadio = new QRadioButton(tr("定时存储"), modeGroupBox);

        if (resultStorageMode == core::VideoResultStorageServer::StorageMode::IMMEDIATE) {
            immediateRadio->setChecked(true);
        } else {
            scheduledRadio->setChecked(true);
        }

        modeLayout->addWidget(immediateRadio);
        modeLayout->addWidget(scheduledRadio);
        layout->addWidget(modeGroupBox);

        // 刷新间隔设置（仅在定时存储模式下有效）
        QGroupBox *intervalGroupBox = new QGroupBox(tr("刷新间隔（毫秒）"), &dialog);
        QVBoxLayout *intervalLayout = new QVBoxLayout(intervalGroupBox);

        QSpinBox *intervalSpinBox = new QSpinBox(intervalGroupBox);
        intervalSpinBox->setRange(100, 60000);  // 100毫秒到60秒
        intervalSpinBox->setValue(resultStorageFlushInterval);
        intervalSpinBox->setSingleStep(100);
        intervalSpinBox->setEnabled(resultStorageMode == core::VideoResultStorageServer::StorageMode::TIMED);

        intervalLayout->addWidget(intervalSpinBox);
        layout->addWidget(intervalGroupBox);

        // 端口设置
        QGroupBox *portGroupBox = new QGroupBox(tr("服务端口"), &dialog);
        QVBoxLayout *portLayout = new QVBoxLayout(portGroupBox);

        QSpinBox *portSpinBox = new QSpinBox(portGroupBox);
        portSpinBox->setRange(1024, 65535);  // 有效的非系统端口范围
        portSpinBox->setValue(resultStorageTcpPort);
        portSpinBox->setSingleStep(1);

        portLayout->addWidget(portSpinBox);
        layout->addWidget(portGroupBox);

        // Modbus寄存器映射设置（仅在Modbus协议下有效）
        QGroupBox *modbusGroupBox = new QGroupBox(tr("Modbus寄存器映射"), &dialog);
        QVBoxLayout *modbusLayout = new QVBoxLayout(modbusGroupBox);

        QTableWidget *registerTable = new QTableWidget(0, 2, modbusGroupBox);
        registerTable->setHorizontalHeaderLabels({tr("字段名"), tr("寄存器地址")});
        registerTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
        registerTable->setSelectionBehavior(QAbstractItemView::SelectRows);

        // 填充当前的寄存器映射
        int row = 0;
        for (const auto& pair : modbusRegisterMap) {
            registerTable->insertRow(row);
            registerTable->setItem(row, 0, new QTableWidgetItem(QString::fromStdString(pair.first)));
            registerTable->setItem(row, 1, new QTableWidgetItem(QString::number(pair.second)));
            row++;
        }

        // 添加/删除按钮
        QHBoxLayout *registerButtonLayout = new QHBoxLayout();
        QPushButton *addRegisterButton = new QPushButton(tr("添加"), modbusGroupBox);
        QPushButton *removeRegisterButton = new QPushButton(tr("删除"), modbusGroupBox);

        registerButtonLayout->addWidget(addRegisterButton);
        registerButtonLayout->addWidget(removeRegisterButton);

        modbusLayout->addWidget(registerTable);
        modbusLayout->addLayout(registerButtonLayout);
        layout->addWidget(modbusGroupBox);

        // 默认根据当前协议类型显示/隐藏Modbus设置
        modbusGroupBox->setVisible(resultStorageProtocolType == core::protocols::ProtocolType::MODBUS);

        // MQTT配置设置（仅在MQTT协议下有效）
        QGroupBox *mqttGroupBox = new QGroupBox(tr("MQTT配置"), &dialog);
        QFormLayout *mqttLayout = new QFormLayout(mqttGroupBox);

        QLineEdit *brokerAddressEdit = new QLineEdit(mqttBrokerAddress, mqttGroupBox);
        QSpinBox *brokerPortSpinBox = new QSpinBox(mqttGroupBox);
        brokerPortSpinBox->setRange(1, 65535);
        brokerPortSpinBox->setValue(mqttBrokerPort);

        QLineEdit *usernameEdit = new QLineEdit(mqttUsername, mqttGroupBox);
        QLineEdit *passwordEdit = new QLineEdit(mqttPassword, mqttGroupBox);
        passwordEdit->setEchoMode(QLineEdit::Password);

        QLineEdit *topicEdit = new QLineEdit(mqttTopic, mqttGroupBox);
        QSpinBox *qosSpinBox = new QSpinBox(mqttGroupBox);
        qosSpinBox->setRange(0, 2);
        qosSpinBox->setValue(mqttQos);

        mqttLayout->addRow(tr("Broker地址:"), brokerAddressEdit);
        mqttLayout->addRow(tr("Broker端口:"), brokerPortSpinBox);
        mqttLayout->addRow(tr("用户名:"), usernameEdit);
        mqttLayout->addRow(tr("密码:"), passwordEdit);
        mqttLayout->addRow(tr("主题:"), topicEdit);
        mqttLayout->addRow(tr("QoS等级:"), qosSpinBox);

        layout->addWidget(mqttGroupBox);

        // 默认根据当前协议类型显示/隐藏MQTT设置
        mqttGroupBox->setVisible(resultStorageProtocolType == core::protocols::ProtocolType::MQTT);

        // 连接信号
        connect(immediateRadio, &QRadioButton::toggled, [intervalSpinBox](bool checked) {
            intervalSpinBox->setEnabled(!checked);  // 即时存储模式下禁用刷新间隔
        });

        connect(protocolComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [modbusGroupBox, mqttGroupBox, protocolComboBox](int index) {
            // 根据选择的协议类型显示/隐藏Modbus和MQTT设置
            core::protocols::ProtocolType type = static_cast<core::protocols::ProtocolType>(
                protocolComboBox->itemData(index).toInt());
            modbusGroupBox->setVisible(type == core::protocols::ProtocolType::MODBUS);
            mqttGroupBox->setVisible(type == core::protocols::ProtocolType::MQTT);
        });

        connect(addRegisterButton, &QPushButton::clicked, [registerTable]() {
            int row = registerTable->rowCount();
            registerTable->insertRow(row);
            registerTable->setItem(row, 0, new QTableWidgetItem(""));
            registerTable->setItem(row, 1, new QTableWidgetItem("0"));
            registerTable->selectRow(row);
            registerTable->editItem(registerTable->item(row, 0));
        });

        connect(removeRegisterButton, &QPushButton::clicked, [registerTable]() {
            QList<QTableWidgetItem*> selectedItems = registerTable->selectedItems();
            if (!selectedItems.isEmpty()) {
                int row = selectedItems.first()->row();
                registerTable->removeRow(row);
            }
        });

        // 按钮
        QDialogButtonBox *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
        layout->addWidget(buttonBox);

        connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
        connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

        if (dialog.exec() == QDialog::Accepted) {
            // 保存设置
            resultStorageMode = immediateRadio->isChecked() ?
                core::VideoResultStorageServer::StorageMode::IMMEDIATE :
                core::VideoResultStorageServer::StorageMode::TIMED;
            resultStorageFlushInterval = intervalSpinBox->value();
            resultStorageTcpPort = portSpinBox->value();
            resultStorageProtocolType = static_cast<core::protocols::ProtocolType>(
                protocolComboBox->currentData().toInt());

            // 保存Modbus寄存器映射
            modbusRegisterMap.clear();
            for (int row = 0; row < registerTable->rowCount(); ++row) {
                QString key = registerTable->item(row, 0)->text();
                QString valueStr = registerTable->item(row, 1)->text();

                if (!key.isEmpty() && !valueStr.isEmpty()) {
                    bool ok;
                    uint16_t value = valueStr.toUShort(&ok);
                    if (ok) {
                        modbusRegisterMap[key.toStdString()] = value;
                    }
                }
            }

            // 保存MQTT配置
            mqttBrokerAddress = brokerAddressEdit->text();
            mqttBrokerPort = brokerPortSpinBox->value();
            mqttUsername = usernameEdit->text();
            mqttPassword = passwordEdit->text();
            mqttTopic = topicEdit->text();
            mqttQos = qosSpinBox->value();

            // 启动结果存储服务
            bool success = videoProcessingCore->start_result_storage_server(
                "results",
                resultStorageMode,
                resultStorageTcpPort,
                resultStorageFlushInterval,
                resultStorageProtocolType
            );

            if (success) {
                // 如果是Modbus协议，设置寄存器映射
                if (resultStorageProtocolType == core::protocols::ProtocolType::MODBUS) {
                    videoProcessingCore->set_modbus_register_map(modbusRegisterMap);
                }

                // 如果是MQTT协议，设置MQTT配置
                if (resultStorageProtocolType == core::protocols::ProtocolType::MQTT) {
                    // TODO: 等待AiVideoCore实现set_mqtt_config方法
                    // videoProcessingCore->set_mqtt_config(
                    //     mqttBrokerAddress.toStdString(),
                    //     mqttBrokerPort,
                    //     mqttUsername.toStdString(),
                    //     mqttPassword.toStdString(),
                    //     mqttTopic.toStdString(),
                    //     mqttQos
                    // );
                }

                // 显示成功消息
                QString modeStr = (resultStorageMode == core::VideoResultStorageServer::StorageMode::IMMEDIATE) ?
                    tr("立即存储") : tr("定时存储");

                QString protocolStr;
                switch (resultStorageProtocolType) {
                    case core::protocols::ProtocolType::TCP:
                        protocolStr = tr("TCP");
                        break;
                    case core::protocols::ProtocolType::MODBUS:
                        protocolStr = tr("Modbus");
                        break;
                    case core::protocols::ProtocolType::MQTT:
                        protocolStr = tr("MQTT");
                        break;
                    case core::protocols::ProtocolType::CUSTOM:
                        protocolStr = tr("自定义");
                        break;
                    default:
                        protocolStr = tr("未知");
                        break;
                }

                utils::showScrollableMessageBox(this, tr("结果存储服务已启用"),
                    tr("处理结果将保存到 results 文件夹中。\n\n") +
                    tr("协议类型: %1\n").arg(protocolStr) +
                    tr("服务已启动在端口: %1\n").arg(resultStorageTcpPort) +
                    tr("存储模式: %1\n").arg(modeStr) +
                    (resultStorageMode == core::VideoResultStorageServer::StorageMode::TIMED ?
                     tr("刷新间隔: %1 毫秒").arg(resultStorageFlushInterval) : ""),
                    QMessageBox::Information);

                // 如果结果查看器已打开，更新存储服务器引用
                if (resultViewerDialog) {
                    resultViewerDialog->set_result_storage_server(videoProcessingCore->get_result_storage_server());
                    resultViewerDialog->load_results_from_server();
                }
            } else {
                // 启动失败，禁用结果存储
                enableResultStorage = false;
                utils::showScrollableMessageBox(this, tr("错误"),
                    tr("结果存储服务启动失败。"),
                    QMessageBox::Critical);
            }
        } else {
            // 用户取消，恢复原来的状态
            enableResultStorage = false;
        }
    } else {
        // 停止结果存储服务
        videoProcessingCore->stop_result_storage_server();

        // 显示消息
        utils::showScrollableMessageBox(this, tr("结果存储服务已停止"),
            tr("处理结果将不再保存到文件中。"),
            QMessageBox::Information);

        // 如果结果查看器已打开，更新存储服务器引用
        if (resultViewerDialog) {
            resultViewerDialog->set_result_storage_server(nullptr);
            resultViewerDialog->update_tcp_status(false, 0, 0);
        }
    }

    // 保存设置
    saveSettings();
}

} // namespace ui
