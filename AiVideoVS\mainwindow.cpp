#include "mainwindow.h"
#include "ui_mainwindow.h"

#include <QDebug>
#include <QFileDialog>
#include <QMessageBox>
#include <QSettings>
#include <QCloseEvent>
#include <QDateTime>
#include <QStandardPaths>
#include <QDir>
#include <QGroupBox>
#include <QHeaderView>
#include <QMouseEvent>
#include <QPoint>
#include <QDesktopWidget>

MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MainWindow),
    isDragging(false),
    titleBar(nullptr),
    minimizeButton(nullptr),
    maximizeButton(nullptr),
    closeButton(nullptr)
{
    ui->setupUi(this);

    // 设置无标题栏窗口
    setWindowFlags(Qt::FramelessWindowHint);

    // 设置窗口标题和大小
    setWindowTitle("AiVideoVS - 多路视频流处理系统");
    resize(1400, 900); // 设置初始大小（在最大化前）

    // 启动时自动最大化窗口
    showMaximized();

    // 设置窗口图标
    setWindowIcon(QIcon(":/icons/play.png"));

    // 初始化界面
    setupUi();

    // 更新最大化按钮的图标和提示（因为窗口已经最大化）
    if (maximizeButton) {
        maximizeButton->setText("❐");
        maximizeButton->setToolTip("还原");
    }

    // 创建菜单和动作
    createActions();
    createMenus();

    // 初始化流管理器
    initializeStreamManager();

    // 加载设置
    loadSettings();

    // 创建更新定时器
    updateTimer = new QTimer(this);
    connect(updateTimer, &QTimer::timeout, this, &MainWindow::updateStreamDisplay);
    updateTimer->start(30); // 30ms更新一次，约33fps

    // 更新配置表格
    updateConfigTable();

    // 应用默认布局
    applyLayout(layoutComboBox->currentIndex());

    // 设置状态栏信息
    statusBar()->showMessage("就绪 - 蓝色扁平化风格界面");
}

MainWindow::~MainWindow()
{
    // 停止所有视频流
    if (streamManager) {
        streamManager->stopAllStreams();
    }

    delete ui;
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    // 保存设置
    saveSettings();

    // 停止所有视频流
    if (streamManager) {
        streamManager->stopAllStreams();
    }

    event->accept();
}

void MainWindow::mousePressEvent(QMouseEvent *event)
{
    // 获取标题栏在窗口中的位置
    QRect titleBarRect;
    if (titleBar) {
        titleBarRect = QRect(titleBar->mapTo(this, QPoint(0, 0)), titleBar->size());

        // 检查鼠标点击是否在标题栏区域内
        if (event->button() == Qt::LeftButton && titleBarRect.contains(event->pos())) {
            isDragging = true;
            dragPosition = event->globalPos() - frameGeometry().topLeft();
            event->accept();
            return; // 不再调用基类方法，避免事件被其他控件处理
        }
    }
    QMainWindow::mousePressEvent(event);
}

void MainWindow::mouseMoveEvent(QMouseEvent *event)
{
    if (isDragging && (event->buttons() & Qt::LeftButton)) {
        move(event->globalPos() - dragPosition);
        event->accept();
    }
    QMainWindow::mouseMoveEvent(event);
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event)
{
    isDragging = false;
    QMainWindow::mouseReleaseEvent(event);
}

bool MainWindow::eventFilter(QObject *watched, QEvent *event)
{
    if (event->type() == QEvent::MouseButtonPress) {
        QString toggleFunction = watched->property("toggleFunction").toString();

        if (toggleFunction == "config") {
            // 切换配置区域的可见性
            QList<QWidget*> contentWidgets = findChildren<QWidget*>("configContent");
            QList<QPushButton*> toggleButtons = qobject_cast<QWidget*>(watched)->findChildren<QPushButton*>("toggleButton");
            QList<QWidget*> containers = findChildren<QWidget*>("configContainer");

            if (!contentWidgets.isEmpty() && !toggleButtons.isEmpty() && !containers.isEmpty()) {
                QWidget* configContent = contentWidgets.first();
                QPushButton* toggleButton = toggleButtons.first();
                QWidget* container = containers.first();

                bool isVisible = !configContent->isVisible();
                configContent->setVisible(isVisible);
                toggleButton->setIcon(QIcon(isVisible ? ":/icons/down-arrow.png" : ":/icons/right-arrow.png"));

                // 设置折叠状态属性
                container->setProperty("collapsed", !isVisible);
                container->style()->unpolish(container);
                container->style()->polish(container);

                return true;
            }
        } else if (toggleFunction == "control") {
            // 切换控制区域的可见性
            QList<QWidget*> contentWidgets = findChildren<QWidget*>("controlContent");
            QList<QPushButton*> toggleButtons = qobject_cast<QWidget*>(watched)->findChildren<QPushButton*>("toggleButton");
            QList<QWidget*> containers = findChildren<QWidget*>("controlContainer");

            if (!contentWidgets.isEmpty() && !toggleButtons.isEmpty() && !containers.isEmpty()) {
                QWidget* controlContent = contentWidgets.first();
                QPushButton* toggleButton = toggleButtons.first();
                QWidget* container = containers.first();

                bool isVisible = !controlContent->isVisible();
                controlContent->setVisible(isVisible);
                toggleButton->setIcon(QIcon(isVisible ? ":/icons/down-arrow.png" : ":/icons/right-arrow.png"));

                // 设置折叠状态属性
                container->setProperty("collapsed", !isVisible);
                container->style()->unpolish(container);
                container->style()->polish(container);

                return true;
            }
        }
    }

    return QMainWindow::eventFilter(watched, event);
}

void MainWindow::minimizeWindow()
{
    showMinimized();
}

void MainWindow::maximizeWindow()
{
    if (isMaximized()) {
        showNormal();
        maximizeButton->setText("□");
        maximizeButton->setToolTip("最大化");
    } else {
        showMaximized();
        maximizeButton->setText("❐");
        maximizeButton->setToolTip("还原");
    }
}

void MainWindow::closeWindow()
{
    close();
}

void MainWindow::setupUi()
{
    // 创建中央部件
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);

    // 创建自定义标题栏
    titleBar = new QWidget(this);
    titleBar->setObjectName("titleBar");
    titleBar->setFixedHeight(36); // 减小高度，使界面更紧凑
    titleBar->setCursor(Qt::SizeAllCursor); // 设置鼠标指针为移动光标，提示用户可以拖动

    QHBoxLayout *titleBarLayout = new QHBoxLayout(titleBar);
    titleBarLayout->setContentsMargins(15, 0, 5, 0); // 增加左侧边距，减少右侧边距
    titleBarLayout->setSpacing(5); // 增加间距

    // 添加图标和标题
    QLabel *iconLabel = new QLabel(this);
    iconLabel->setPixmap(QIcon(":/icons/play.png").pixmap(16, 16));

    QLabel *titleLabel = new QLabel("AiVideoVS - 多路视频流处理系统", this);
    titleLabel->setObjectName("titleLabel");

    titleBarLayout->addWidget(iconLabel);
    titleBarLayout->addSpacing(5);
    titleBarLayout->addWidget(titleLabel);
    titleBarLayout->addStretch();

    // 添加窗口控制按钮
    minimizeButton = new QPushButton("—", this);
    minimizeButton->setObjectName("minimizeButton");
    minimizeButton->setToolTip("最小化");
    minimizeButton->setFixedSize(40, 28);
    minimizeButton->setFocusPolicy(Qt::NoFocus); // 防止按钮获取焦点

    maximizeButton = new QPushButton("□", this);
    maximizeButton->setObjectName("maximizeButton");
    maximizeButton->setToolTip("最大化");
    maximizeButton->setFixedSize(40, 28);
    maximizeButton->setFocusPolicy(Qt::NoFocus); // 防止按钮获取焦点

    closeButton = new QPushButton("×", this);
    closeButton->setObjectName("closeButton");
    closeButton->setToolTip("关闭");
    closeButton->setFixedSize(40, 28);
    closeButton->setFocusPolicy(Qt::NoFocus); // 防止按钮获取焦点

    // 连接按钮信号
    connect(minimizeButton, &QPushButton::clicked, this, &MainWindow::minimizeWindow);
    connect(maximizeButton, &QPushButton::clicked, this, &MainWindow::maximizeWindow);
    connect(closeButton, &QPushButton::clicked, this, &MainWindow::closeWindow);

    titleBarLayout->addWidget(minimizeButton);
    titleBarLayout->addWidget(maximizeButton);
    titleBarLayout->addWidget(closeButton);

    // 添加标题栏到主布局（放在最上方）
    mainLayout->addWidget(titleBar);

    // 创建内容容器
    QWidget *contentWidget = new QWidget(this);
    contentWidget->setObjectName("contentWidget");
    QVBoxLayout *contentLayout = new QVBoxLayout(contentWidget);
    contentLayout->setContentsMargins(12, 10, 12, 10);
    contentLayout->setSpacing(6);

    // 将内容容器添加到主布局，并设置为拉伸
    mainLayout->addWidget(contentWidget);
    mainLayout->setStretchFactor(contentWidget, 1);

    // 创建配置区域（添加可折叠功能）
    QWidget *configContainer = new QWidget(this);
    configContainer->setObjectName("configContainer");
    QVBoxLayout *configContainerLayout = new QVBoxLayout(configContainer);
    configContainerLayout->setContentsMargins(0, 0, 0, 0);
    configContainerLayout->setSpacing(0);

    // 创建配置区域标题栏
    QWidget *configTitleBar = new QWidget(this);
    configTitleBar->setObjectName("sectionTitleBar");
    configTitleBar->setCursor(Qt::PointingHandCursor);
    QHBoxLayout *configTitleLayout = new QHBoxLayout(configTitleBar);
    configTitleLayout->setContentsMargins(5, 1, 5, 1);
    configTitleLayout->setSpacing(3);

    QLabel *configTitleLabel = new QLabel("视频流配置", this);
    configTitleLabel->setObjectName("sectionTitle");
    configTitleLabel->setFont(QFont("Microsoft YaHei", 8, QFont::Bold));

    QPushButton *configToggleButton = new QPushButton(this);
    configToggleButton->setObjectName("toggleButton");
    configToggleButton->setFixedSize(12, 12);
    configToggleButton->setIconSize(QSize(8, 8));
    configToggleButton->setIcon(QIcon(":/icons/down-arrow.png"));
    configToggleButton->setFlat(true);
    configToggleButton->setCursor(Qt::PointingHandCursor);

    configTitleLayout->addWidget(configTitleLabel);
    configTitleLayout->addStretch();
    configTitleLayout->addWidget(configToggleButton);

    // 创建配置内容区域
    QWidget *configContent = new QWidget(this);
    configContent->setObjectName("configContent");
    QVBoxLayout *configContentLayout = new QVBoxLayout(configContent);
    configContentLayout->setContentsMargins(0, 3, 0, 0);
    configContentLayout->setSpacing(3);

    // 创建配置表格
    configTable = new QTableWidget(0, 5, this);
    configTable->setHorizontalHeaderLabels({"视频路径", "项目路径", "端口", "状态", "操作"});

    // 设置表格列宽
    configTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch); // 视频路径列自适应
    configTable->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Stretch); // 项目路径列自适应
    configTable->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Fixed);   // 端口列固定宽度
    configTable->horizontalHeader()->setSectionResizeMode(3, QHeaderView::Fixed);   // 状态列固定宽度
    configTable->horizontalHeader()->setSectionResizeMode(4, QHeaderView::Fixed);   // 操作列固定宽度

    // 设置固定列的宽度
    configTable->setColumnWidth(2, 80);  // 端口列
    configTable->setColumnWidth(3, 100); // 状态列
    configTable->setColumnWidth(4, 250); // 操作列 - 增加宽度以容纳更大的按钮

    // 设置表格最小高度
    configTable->setMinimumHeight(120);

    configTable->verticalHeader()->setVisible(true);
    configTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents); // 让行高自适应内容
    configTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    configTable->setEditTriggers(QAbstractItemView::DoubleClicked);
    configContentLayout->addWidget(configTable);

    // 创建配置按钮区域
    QHBoxLayout *configButtonLayout = new QHBoxLayout();
    configButtonLayout->setSpacing(15); // 增加按钮之间的间距

    // 创建添加视频流按钮
    addStreamButton = new QPushButton("添加视频流", this);
    addStreamButton->setObjectName("configButton"); // 设置对象名，用于样式表
    addStreamButton->setIcon(QIcon(":/icons/add.png"));
    addStreamButton->setIconSize(QSize(14, 14)); // 减小图标尺寸
    addStreamButton->setMinimumHeight(26); // 减小按钮高度
    addStreamButton->setMinimumWidth(120); // 减小最小宽度

    // 设置字体
    QFont buttonFont = addStreamButton->font();
    buttonFont.setPointSize(9); // 减小字体大小
    buttonFont.setBold(true); // 保持粗体
    addStreamButton->setFont(buttonFont);

    // 创建移除视频流按钮
    removeStreamButton = new QPushButton("移除视频流", this);
    removeStreamButton->setObjectName("configButton"); // 设置对象名，用于样式表
    removeStreamButton->setIcon(QIcon(":/icons/remove.png"));
    removeStreamButton->setIconSize(QSize(14, 14)); // 减小图标尺寸
    removeStreamButton->setMinimumHeight(26); // 减小按钮高度
    removeStreamButton->setMinimumWidth(120); // 减小最小宽度
    removeStreamButton->setFont(buttonFont); // 使用相同的字体

    // 添加按钮到布局
    configButtonLayout->addWidget(addStreamButton);
    configButtonLayout->addWidget(removeStreamButton);
    configButtonLayout->addStretch();

    // 添加到配置内容布局
    configContentLayout->addLayout(configButtonLayout);

    // 添加配置标题栏和内容到配置容器
    configContainerLayout->addWidget(configTitleBar);
    configContainerLayout->addWidget(configContent);

    // 添加配置容器到内容布局
    contentLayout->addWidget(configContainer);

    // 创建控制区域（添加可折叠功能）
    QWidget *controlContainer = new QWidget(this);
    controlContainer->setObjectName("controlContainer");
    QVBoxLayout *controlContainerLayout = new QVBoxLayout(controlContainer);
    controlContainerLayout->setContentsMargins(0, 0, 0, 0);
    controlContainerLayout->setSpacing(0);

    // 创建控制区域标题栏
    QWidget *controlTitleBar = new QWidget(this);
    controlTitleBar->setObjectName("sectionTitleBar");
    controlTitleBar->setCursor(Qt::PointingHandCursor);
    QHBoxLayout *controlTitleLayout = new QHBoxLayout(controlTitleBar);
    controlTitleLayout->setContentsMargins(5, 1, 5, 1);
    controlTitleLayout->setSpacing(3);

    QLabel *controlTitleLabel = new QLabel("控制", this);
    controlTitleLabel->setObjectName("sectionTitle");
    controlTitleLabel->setFont(QFont("Microsoft YaHei", 8, QFont::Bold));

    QPushButton *controlToggleButton = new QPushButton(this);
    controlToggleButton->setObjectName("toggleButton");
    controlToggleButton->setFixedSize(12, 12);
    controlToggleButton->setIconSize(QSize(8, 8));
    controlToggleButton->setIcon(QIcon(":/icons/down-arrow.png"));
    controlToggleButton->setFlat(true);
    controlToggleButton->setCursor(Qt::PointingHandCursor);

    controlTitleLayout->addWidget(controlTitleLabel);
    controlTitleLayout->addStretch();
    controlTitleLayout->addWidget(controlToggleButton);

    // 创建控制内容区域
    QWidget *controlContent = new QWidget(this);
    controlContent->setObjectName("controlContent");
    QHBoxLayout *controlLayout = new QHBoxLayout(controlContent);
    controlLayout->setContentsMargins(5, 3, 5, 3);
    controlLayout->setSpacing(3);

    // 创建布局选择下拉框
    QLabel *layoutLabel = new QLabel("显示布局:", this);
    layoutLabel->setFont(QFont("Microsoft YaHei", 9));

    layoutComboBox = new QComboBox(this);
    layoutComboBox->setFixedHeight(24);
    layoutComboBox->setFont(QFont("Microsoft YaHei", 9));
    layoutComboBox->addItem("单个视频");
    layoutComboBox->addItem("2x1 布局");
    layoutComboBox->addItem("2x2 布局");
    layoutComboBox->addItem("3x2 布局");
    layoutComboBox->addItem("3x3 布局");
    controlLayout->addWidget(layoutLabel);
    controlLayout->addWidget(layoutComboBox);

    controlLayout->addStretch();

    // 创建控制按钮
    startAllButton = new QPushButton("全部启动", this);
    pauseAllButton = new QPushButton("全部暂停", this);
    stopAllButton = new QPushButton("全部停止", this);

    // 设置按钮图标
    startAllButton->setIcon(QIcon(":/icons/play.png"));
    pauseAllButton->setIcon(QIcon(":/icons/pause.png"));
    stopAllButton->setIcon(QIcon(":/icons/stop.png"));

    // 设置图标大小
    startAllButton->setIconSize(QSize(14, 14));
    pauseAllButton->setIconSize(QSize(14, 14));
    stopAllButton->setIconSize(QSize(14, 14));

    // 设置按钮大小
    startAllButton->setMinimumHeight(26);
    pauseAllButton->setMinimumHeight(26);
    stopAllButton->setMinimumHeight(26);

    // 设置字体
    QFont controlButtonFont = startAllButton->font();
    controlButtonFont.setPointSize(9);
    startAllButton->setFont(controlButtonFont);
    pauseAllButton->setFont(controlButtonFont);
    stopAllButton->setFont(controlButtonFont);

    controlLayout->addWidget(startAllButton);
    controlLayout->addWidget(pauseAllButton);
    controlLayout->addWidget(stopAllButton);

    // 添加控制标题栏和内容到控制容器
    controlContainerLayout->addWidget(controlTitleBar);
    controlContainerLayout->addWidget(controlContent);

    // 添加控制容器到内容布局
    contentLayout->addWidget(controlContainer);

    // 创建视频显示区域
    QGroupBox *videoGroupBox = new QGroupBox("视频显示", this);
    videoLayout = new QGridLayout(videoGroupBox);

    // 添加视频显示区域到内容布局
    contentLayout->addWidget(videoGroupBox);

    // 设置视频显示区域占比较大
    contentLayout->setStretchFactor(configContainer, 1);
    contentLayout->setStretchFactor(controlContainer, 1);
    contentLayout->setStretchFactor(videoGroupBox, 8);

    // 连接信号和槽
    connect(addStreamButton, &QPushButton::clicked, this, &MainWindow::on_addStreamButton_clicked);
    connect(removeStreamButton, &QPushButton::clicked, this, &MainWindow::on_removeStreamButton_clicked);
    connect(startAllButton, &QPushButton::clicked, this, &MainWindow::on_startAllButton_clicked);
    connect(pauseAllButton, &QPushButton::clicked, this, &MainWindow::on_pauseAllButton_clicked);
    connect(stopAllButton, &QPushButton::clicked, this, &MainWindow::on_stopAllButton_clicked);
    connect(layoutComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::on_layoutComboBox_currentIndexChanged);
    connect(configTable, &QTableWidget::cellClicked, this, &MainWindow::on_configTable_cellClicked);
    connect(configTable, &QTableWidget::cellChanged, this, &MainWindow::on_configTable_cellChanged);

    // 连接折叠按钮信号
    connect(configToggleButton, &QPushButton::clicked, [=]() {
        bool isVisible = !configContent->isVisible();
        configContent->setVisible(isVisible);
        configToggleButton->setIcon(QIcon(isVisible ? ":/icons/down-arrow.png" : ":/icons/right-arrow.png"));
        configContainer->setProperty("collapsed", !isVisible);
        configContainer->style()->unpolish(configContainer);
        configContainer->style()->polish(configContainer);
    });

    connect(controlToggleButton, &QPushButton::clicked, [=]() {
        bool isVisible = !controlContent->isVisible();
        controlContent->setVisible(isVisible);
        controlToggleButton->setIcon(QIcon(isVisible ? ":/icons/down-arrow.png" : ":/icons/right-arrow.png"));
        controlContainer->setProperty("collapsed", !isVisible);
        controlContainer->style()->unpolish(controlContainer);
        controlContainer->style()->polish(controlContainer);
    });

    // 创建一个简单的点击处理函数
    auto toggleConfigVisibility = [=]() {
        bool isVisible = !configContent->isVisible();
        configContent->setVisible(isVisible);
        configToggleButton->setIcon(QIcon(isVisible ? ":/icons/down-arrow.png" : ":/icons/right-arrow.png"));
        configContainer->setProperty("collapsed", !isVisible);
        configContainer->style()->unpolish(configContainer);
        configContainer->style()->polish(configContainer);
    };

    auto toggleControlVisibility = [=]() {
        bool isVisible = !controlContent->isVisible();
        controlContent->setVisible(isVisible);
        controlToggleButton->setIcon(QIcon(isVisible ? ":/icons/down-arrow.png" : ":/icons/right-arrow.png"));
        controlContainer->setProperty("collapsed", !isVisible);
        controlContainer->style()->unpolish(controlContainer);
        controlContainer->style()->polish(controlContainer);
    };

    // 为标题栏添加事件过滤器
    configTitleBar->installEventFilter(this);
    configTitleBar->setProperty("toggleFunction", "config");

    controlTitleBar->installEventFilter(this);
    controlTitleBar->setProperty("toggleFunction", "control");

    // 直接连接折叠按钮的点击事件
    connect(configToggleButton, &QPushButton::clicked, toggleConfigVisibility);
    connect(controlToggleButton, &QPushButton::clicked, toggleControlVisibility);
}

void MainWindow::createActions()
{
    // 创建菜单动作
}

void MainWindow::createMenus()
{
    // 隐藏默认菜单栏
    menuBar()->setVisible(false);

    // 创建自定义菜单栏
    QMenuBar *customMenuBar = new QMenuBar(centralWidget());
    customMenuBar->setObjectName("customMenuBar");

    // 将自定义菜单栏添加到主布局中，位于标题栏下方
    QVBoxLayout *mainLayout = qobject_cast<QVBoxLayout*>(centralWidget()->layout());
    if (mainLayout) {
        // 在标题栏和内容容器之间插入菜单栏
        mainLayout->insertWidget(1, customMenuBar);
    }

    // 创建菜单
    QMenu *fileMenu = customMenuBar->addMenu("文件");
    QMenu *viewMenu = customMenuBar->addMenu("视图");
    QMenu *helpMenu = customMenuBar->addMenu("帮助");

    // 文件菜单
    QAction *saveConfigAction = new QAction("保存配置", this);
    QAction *loadConfigAction = new QAction("加载配置", this);
    QAction *exitAction = new QAction("退出", this);

    connect(saveConfigAction, &QAction::triggered, this, &MainWindow::on_actionSaveConfig_triggered);
    connect(loadConfigAction, &QAction::triggered, this, &MainWindow::on_actionLoadConfig_triggered);
    connect(exitAction, &QAction::triggered, this, &MainWindow::on_actionExit_triggered);

    fileMenu->addAction(saveConfigAction);
    fileMenu->addAction(loadConfigAction);
    fileMenu->addSeparator();
    fileMenu->addAction(exitAction);

    // 帮助菜单
    QAction *aboutAction = new QAction("关于", this);
    connect(aboutAction, &QAction::triggered, this, &MainWindow::on_actionAbout_triggered);
    helpMenu->addAction(aboutAction);
}

std::string safe_getenv(const char* var) {
    const char* value = std::getenv(var);
    return value ? std::string(value) : std::string();
}

void MainWindow::initializeStreamManager()
{
    // 初始化VisionFlow环境
    // core::VideoProcessingCore::initialize_visionflow("9733c801000702014f0d000200130023", "192.168.0.169");
    // 从环境变量 VFLOW_LICENSE_ID 和 VFLOW_LICENSE_ADDR 获取许可证ID和服务器地址
    std::string licenseId = safe_getenv("VFLOW_LICENSE_ID");
    std::string licenseAddr = safe_getenv("VFLOW_LICENSE_ADDR");
    qDebug() << "VFLOW_LICENSE_ID: " << licenseId.c_str();
    qDebug() << "VFLOW_LICENSE_ADDR: " << licenseAddr.c_str();
    core::VideoProcessingCore::initialize_visionflow(licenseId, licenseAddr);
    // 创建流管理器
    streamManager = new StreamManager(this);
}

void MainWindow::updateConfigTable()
{
    // 断开信号连接，避免触发cellChanged
    disconnect(configTable, &QTableWidget::cellChanged, this, &MainWindow::on_configTable_cellChanged);

    // 清空表格
    configTable->clearContents();
    configTable->setRowCount(0);

    // 获取流配置
    QList<StreamConfig> configs = streamManager->getStreamConfigs();

    // 填充表格
    for (int i = 0; i < configs.size(); i++) {
        configTable->insertRow(i);

        // 视频路径
        QTableWidgetItem *videoItem = new QTableWidgetItem(configs[i].videoPath);
        configTable->setItem(i, 0, videoItem);

        // 项目路径
        QTableWidgetItem *projectItem = new QTableWidgetItem(configs[i].projectPath);
        configTable->setItem(i, 1, projectItem);

        // 端口
        QTableWidgetItem *portItem = new QTableWidgetItem(QString::number(configs[i].resultStoragePort));
        configTable->setItem(i, 2, portItem);

        // 状态
        QString status;
        if (configs[i].isActive) {
            if (configs[i].isPaused) {
                status = "已暂停";
            } else {
                status = "运行中";
            }
        } else {
            status = "未启动";
        }
        QTableWidgetItem *statusItem = new QTableWidgetItem(status);
        statusItem->setFlags(statusItem->flags() & ~Qt::ItemIsEditable); // 设置为不可编辑
        configTable->setItem(i, 3, statusItem);

        // 操作按钮
        QWidget *buttonWidget = new QWidget(configTable);
        QHBoxLayout *buttonLayout = new QHBoxLayout(buttonWidget);
        buttonLayout->setContentsMargins(2, 2, 2, 2); // 减小边距，使按钮更大
        buttonLayout->setSpacing(5); // 减小按钮之间的间距，使每个按钮更大

        // 创建浏览视频按钮
        QPushButton *browseVideoButton = new QPushButton("浏览视频", buttonWidget);
        browseVideoButton->setObjectName("cellButton"); // 使用新的对象名，以便应用特殊样式
        browseVideoButton->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred); // 让按钮高度自适应
        browseVideoButton->setMinimumWidth(70); // 保持最小宽度

        // 设置字体
        QFont tableButtonFont;
        tableButtonFont.setPointSize(9); // 减小字体大小
        tableButtonFont.setBold(true); // 保持粗体
        browseVideoButton->setFont(tableButtonFont);

        // 创建浏览项目按钮
        QPushButton *browseProjectButton = new QPushButton("浏览项目", buttonWidget);
        browseProjectButton->setObjectName("cellButton"); // 使用新的对象名，以便应用特殊样式
        browseProjectButton->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred); // 让按钮高度自适应
        browseProjectButton->setMinimumWidth(70); // 保持最小宽度
        browseProjectButton->setFont(tableButtonFont); // 使用相同的字体

        // 设置行属性
        browseVideoButton->setProperty("row", i);
        browseProjectButton->setProperty("row", i);

        // 连接信号
        connect(browseVideoButton, &QPushButton::clicked, this, &MainWindow::browseVideoFile);
        connect(browseProjectButton, &QPushButton::clicked, this, &MainWindow::browseProjectFile);

        // 添加按钮到布局
        buttonLayout->addWidget(browseVideoButton);
        buttonLayout->addWidget(browseProjectButton);

        // 设置单元格控件
        configTable->setCellWidget(i, 4, buttonWidget);
    }

    // 重新连接信号
    connect(configTable, &QTableWidget::cellChanged, this, &MainWindow::on_configTable_cellChanged);
}

void MainWindow::updateVideoWidgets()
{
    // 清除现有的视频控件
    for (VideoStreamWidget *widget : videoWidgets) {
        // 断开信号连接
        disconnect(widget, &VideoStreamWidget::startStream, this, &MainWindow::onStartStream);
        disconnect(widget, &VideoStreamWidget::pauseStream, this, &MainWindow::onPauseStream);
        disconnect(widget, &VideoStreamWidget::stopStream, this, &MainWindow::onStopStream);

        videoLayout->removeWidget(widget);
        widget->deleteLater();
    }
    videoWidgets.clear();

    // 获取流配置
    QList<StreamConfig> configs = streamManager->getStreamConfigs();

    // 创建新的视频控件
    for (int i = 0; i < configs.size(); i++) {
        VideoStreamWidget *widget = new VideoStreamWidget(this);
        widget->setStreamId(i);
        widget->setStreamInfo(configs[i].videoPath, configs[i].projectPath, configs[i].resultStoragePort);

        // 连接信号和槽
        connect(widget, &VideoStreamWidget::startStream, this, &MainWindow::onStartStream);
        connect(widget, &VideoStreamWidget::pauseStream, this, &MainWindow::onPauseStream);
        connect(widget, &VideoStreamWidget::stopStream, this, &MainWindow::onStopStream);

        videoWidgets.append(widget);
    }

    // 根据视频流数量自动选择合适的布局
    int layoutIndex = layoutComboBox->currentIndex();

    // 如果当前布局是单个视频(0)，但有多个视频流，则自动切换到更合适的布局
    if (layoutIndex == 0 && configs.size() > 1) {
        if (configs.size() == 2) {
            // 如果有2个视频流，使用2x1布局
            layoutIndex = 1;
        } else if (configs.size() <= 4) {
            // 如果有3-4个视频流，使用2x2布局
            layoutIndex = 2;
        } else if (configs.size() <= 6) {
            // 如果有5-6个视频流，使用3x2布局
            layoutIndex = 3;
        } else {
            // 如果有更多视频流，使用3x3布局
            layoutIndex = 4;
        }

        // 更新下拉框选择
        layoutComboBox->setCurrentIndex(layoutIndex);
    } else {
        // 应用当前布局
        applyLayout(layoutIndex);
    }
}

void MainWindow::applyLayout(int layoutType)
{
    // 清除现有布局
    QLayoutItem *child;
    while ((child = videoLayout->takeAt(0)) != nullptr) {
        videoLayout->removeItem(child);
    }

    // 根据布局类型和视频控件数量布局
    int count = videoWidgets.size();

    switch (layoutType) {
    case 0: // 单个视频
        for (int i = 0; i < count; i++) {
            videoWidgets[i]->setVisible(i == 0);
            if (i == 0) {
                videoLayout->addWidget(videoWidgets[i], 0, 0);
            }
        }
        break;
    case 1: // 2x1布局
        for (int i = 0; i < count; i++) {
            videoWidgets[i]->setVisible(i < 2);
            if (i < 2) {
                videoLayout->addWidget(videoWidgets[i], i, 0);
            }
        }
        break;
    case 2: // 2x2布局
        for (int i = 0; i < count; i++) {
            videoWidgets[i]->setVisible(i < 4);
            if (i < 4) {
                videoLayout->addWidget(videoWidgets[i], i / 2, i % 2);
            }
        }
        break;
    case 3: // 3x2布局
        for (int i = 0; i < count; i++) {
            videoWidgets[i]->setVisible(i < 6);
            if (i < 6) {
                videoLayout->addWidget(videoWidgets[i], i / 3, i % 3);
            }
        }
        break;
    case 4: // 3x3布局
        for (int i = 0; i < count; i++) {
            videoWidgets[i]->setVisible(i < 9);
            if (i < 9) {
                videoLayout->addWidget(videoWidgets[i], i / 3, i % 3);
            }
        }
        break;
    }
}

void MainWindow::saveSettings()
{
    QSettings settings("AiVideoVS", "AiVideoVS");

    // 保存窗口状态
    settings.setValue("geometry", saveGeometry());
    settings.setValue("windowState", saveState());
    settings.setValue("isMaximized", isMaximized());

    // 保存布局设置
    settings.setValue("layoutType", layoutComboBox->currentIndex());

    // 保存流配置
    streamManager->saveConfig("config.aivs");
}

void MainWindow::loadSettings()
{
    QSettings settings("AiVideoVS", "AiVideoVS");

    // 恢复窗口状态
    if (settings.contains("geometry")) {
        restoreGeometry(settings.value("geometry").toByteArray());
    }
    if (settings.contains("windowState")) {
        restoreState(settings.value("windowState").toByteArray());
    }

    // 根据保存的设置确定是否最大化窗口
    bool shouldMaximize = settings.value("isMaximized", true).toBool();
    if (shouldMaximize && !isMaximized()) {
        showMaximized();

        // 更新最大化按钮的图标和提示
        if (maximizeButton) {
            maximizeButton->setText("❐");
            maximizeButton->setToolTip("还原");
        }
    }

    // 恢复布局设置
    if (settings.contains("layoutType")) {
        layoutComboBox->setCurrentIndex(settings.value("layoutType").toInt());
    }

    // 加载流配置
    if (QFile::exists("config.aivs")) {
        // 检查文件是否可读
        QFile configFile("config.aivs");
        if (!configFile.open(QIODevice::ReadOnly)) {
            QMessageBox::warning(this, QString::fromUtf8("加载失败"),
                               QString::fromUtf8("无法打开配置文件config.aivs: %1").arg(configFile.errorString()));
            return;
        }
        configFile.close();

        // 创建进度对话框
        QProgressDialog* progressDialog = createProgressDialog(
            QString::fromUtf8("加载配置"),
            QString::fromUtf8("正在加载配置文件..."));
        progressDialog->show();
        processEvents();

        // 加载配置
        QElapsedTimer timer;
        timer.start();

        // 使用try-catch捕获可能的异常
        bool success = false;
        try {
            success = streamManager->loadConfig("config.aivs");
        } catch (const std::exception& e) {
            qWarning() << QString::fromUtf8("加载配置异常:") << QString::fromUtf8(e.what());
            success = false;
        } catch (...) {
            qWarning() << QString::fromUtf8("加载配置时发生未知异常");
            success = false;
        }

        qint64 elapsed = timer.elapsed();

        // 关闭进度对话框
        progressDialog->close();
        delete progressDialog;

        if (success) {
            updateConfigTable();

            // 获取流配置数量
            QList<StreamConfig> configs = streamManager->getStreamConfigs();
            qDebug() << QString::fromUtf8("加载了") << configs.size() << QString::fromUtf8("个视频流配置");

            // 如果有多个视频流，自动选择合适的布局
            if (configs.size() > 1) {
                int layoutIndex = 0;
                if (configs.size() == 2) {
                    layoutIndex = 1; // 2x1布局
                } else if (configs.size() <= 4) {
                    layoutIndex = 2; // 2x2布局
                } else if (configs.size() <= 6) {
                    layoutIndex = 3; // 3x2布局
                } else {
                    layoutIndex = 4; // 3x3布局
                }

                // 设置布局下拉框
                layoutComboBox->setCurrentIndex(layoutIndex);
                qDebug() << QString::fromUtf8("自动选择布局:") << layoutIndex;
            }

            updateVideoWidgets();
            qDebug() << QString::fromUtf8("成功加载默认配置文件，耗时:") << elapsed << "ms";
        } else {
            QMessageBox::warning(this, QString::fromUtf8("加载失败"),
                               QString::fromUtf8("无法加载默认配置文件，请检查config.aivs是否有效\n"
                                              "可能的原因：\n"
                                              "1. 文件格式不正确\n"
                                              "2. 文件内容损坏\n"
                                              "3. 配置中的路径不存在"));
        }
    }
}

void MainWindow::updateStreamDisplay()
{
    // 更新视频显示
    for (int i = 0; i < videoWidgets.size(); i++) {
        // 获取当前帧和处理结果
        cv::Mat frame = streamManager->getFrame(i);
        ai::FrameResult result = streamManager->getResult(i);

        // 如果有处理结果，使用带结果的更新方法
        if (!result.ext_info.empty()) {
            videoWidgets[i]->updateFrameWithResult(frame, result);
        } else {
            videoWidgets[i]->updateFrame(frame);
        }
    }
}

void MainWindow::browseVideoFile()
{
    // 获取发送者
    QPushButton *button = qobject_cast<QPushButton*>(sender());
    if (!button) return;

    // 获取行号
    int row = button->property("row").toInt();

    // 打开文件对话框
    QString filePath = QFileDialog::getOpenFileName(this, "选择视频文件",
                                                   QDir::homePath(),
                                                   "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)");

    if (!filePath.isEmpty()) {
        // 更新表格
        configTable->item(row, 0)->setText(filePath);

        // 更新流配置
        streamManager->updateVideoPath(row, filePath);

        // 更新视频控件
        if (row < videoWidgets.size()) {
            // 获取当前端口
            QList<StreamConfig> configs = streamManager->getStreamConfigs();
            int port = 8888;
            if (row < configs.size()) {
                port = configs[row].resultStoragePort;
            }
            videoWidgets[row]->setStreamInfo(filePath, configTable->item(row, 1)->text(), port);
        }
    }
}

void MainWindow::browseProjectFile()
{
    // 获取发送者
    QPushButton *button = qobject_cast<QPushButton*>(sender());
    if (!button) return;

    // 获取行号
    int row = button->property("row").toInt();

    // 打开文件对话框
    QString filePath = QFileDialog::getOpenFileName(this, "选择项目文件",
                                                   QDir::homePath(),
                                                   "项目文件 (*.aivp);;所有文件 (*)");

    if (!filePath.isEmpty()) {
        // 更新表格
        configTable->item(row, 1)->setText(filePath);

        // 更新流配置
        streamManager->updateProjectPath(row, filePath);

        // 更新视频控件
        if (row < videoWidgets.size()) {
            // 获取当前端口
            QList<StreamConfig> configs = streamManager->getStreamConfigs();
            int port = 8888;
            if (row < configs.size()) {
                port = configs[row].resultStoragePort;
            }
            videoWidgets[row]->setStreamInfo(configTable->item(row, 0)->text(), filePath, port);
        }
    }
}

// 槽函数实现
void MainWindow::on_actionExit_triggered()
{
    close();
}

void MainWindow::on_actionAbout_triggered()
{
    QMessageBox::about(this, "关于 AiVideoVS",
                      "AiVideoVS - 多路视频流处理系统\n\n"
                      "版本: 1.0.0\n"
                      "基于 AiVideoCore 库开发\n\n"
                      "功能:\n"
                      "- 多路视频流处理\n"
                      "- 视频和项目绑定管理\n"
                      "- 视频处理控制\n");
}

void MainWindow::on_actionSaveConfig_triggered()
{
    QString filePath = QFileDialog::getSaveFileName(this, "保存配置",
                                                  QDir::homePath(),
                                                  "AiVideoVS配置文件 (*.aivs)");

    if (!filePath.isEmpty()) {
        if (streamManager->saveConfig(filePath)) {
            QMessageBox::information(this, "保存成功", "配置已保存到: " + filePath);
        } else {
            QMessageBox::warning(this, "保存失败", "无法保存配置到: " + filePath);
        }
    }
}

void MainWindow::on_actionLoadConfig_triggered()
{
    QString filePath = QFileDialog::getOpenFileName(this, "加载配置",
                                                  QDir::homePath(),
                                                  "AiVideoVS配置文件 (*.aivs)");

    if (!filePath.isEmpty()) {
        // 创建进度对话框
        QProgressDialog* progressDialog = createProgressDialog("加载配置", "正在加载配置文件: " + filePath);
        progressDialog->show();
        processEvents();

        // 加载配置
        QElapsedTimer timer;
        timer.start();
        bool success = streamManager->loadConfig(filePath);
        qint64 elapsed = timer.elapsed();

        // 关闭进度对话框
        progressDialog->close();
        delete progressDialog;

        if (success) {
            updateConfigTable();

            // 获取流配置数量
            QList<StreamConfig> configs = streamManager->getStreamConfigs();
            qDebug() << QString::fromUtf8("加载了") << configs.size() << QString::fromUtf8("个视频流配置");

            // 如果有多个视频流，自动选择合适的布局
            if (configs.size() > 1) {
                int layoutIndex = 0;
                if (configs.size() == 2) {
                    layoutIndex = 1; // 2x1布局
                } else if (configs.size() <= 4) {
                    layoutIndex = 2; // 2x2布局
                } else if (configs.size() <= 6) {
                    layoutIndex = 3; // 3x2布局
                } else {
                    layoutIndex = 4; // 3x3布局
                }

                // 设置布局下拉框
                layoutComboBox->setCurrentIndex(layoutIndex);
                qDebug() << QString::fromUtf8("自动选择布局:") << layoutIndex;
            }

            updateVideoWidgets();
            QMessageBox::information(this, QString::fromUtf8("加载成功"),
                                    QString::fromUtf8("配置已从以下位置加载: %1\n加载耗时: %2 毫秒")
                                    .arg(filePath)
                                    .arg(elapsed));
        } else {
            QMessageBox::warning(this, "加载失败",
                               QString("无法从以下位置加载配置: %1\n请检查文件格式是否正确")
                               .arg(filePath));
        }
    }
}

void MainWindow::on_addStreamButton_clicked()
{
    // 查找可用端口
    int port = findAvailablePort(8888);
    if (port <= 0) {
        QMessageBox::warning(this, QString::fromUtf8("添加失败"),
                           QString::fromUtf8("无法找到可用端口，请关闭一些应用程序后重试"));
        return;
    }

    // 添加新的流配置
    StreamConfig config;
    config.resultStoragePort = port;
    streamManager->addStream(config);

    qDebug() << QString::fromUtf8("添加新视频流，分配端口:") << port;

    // 更新表格和视频控件
    updateConfigTable();
    updateVideoWidgets();
}

void MainWindow::on_removeStreamButton_clicked()
{
    // 获取选中的行
    QList<QTableWidgetItem*> selectedItems = configTable->selectedItems();
    if (selectedItems.isEmpty()) {
        QMessageBox::warning(this, "警告", "请先选择要移除的视频流");
        return;
    }

    int row = selectedItems.first()->row();

    // 确认对话框
    QMessageBox::StandardButton reply = QMessageBox::question(this, "确认移除",
                                                            "确定要移除选中的视频流吗？",
                                                            QMessageBox::Yes | QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        // 移除流配置
        streamManager->removeStream(row);

        // 更新表格和视频控件
        updateConfigTable();
        updateVideoWidgets();
    }
}

void MainWindow::on_configTable_cellClicked(int row, int column)
{
    // 处理单元格点击事件
}

void MainWindow::on_configTable_cellChanged(int row, int column)
{
    // 处理单元格内容变化事件
    if (column == 0) { // 视频路径
        QString videoPath = configTable->item(row, 0)->text();
        streamManager->updateVideoPath(row, videoPath);

        // 更新视频控件
        if (row < videoWidgets.size()) {
            // 获取当前端口
            QList<StreamConfig> configs = streamManager->getStreamConfigs();
            int port = 8888;
            if (row < configs.size()) {
                port = configs[row].resultStoragePort;
            }
            videoWidgets[row]->setStreamInfo(videoPath, configTable->item(row, 1)->text(), port);
        }
    } else if (column == 1) { // 项目路径
        QString projectPath = configTable->item(row, 1)->text();
        streamManager->updateProjectPath(row, projectPath);

        // 更新视频控件
        if (row < videoWidgets.size()) {
            // 获取当前端口
            QList<StreamConfig> configs = streamManager->getStreamConfigs();
            int port = 8888;
            if (row < configs.size()) {
                port = configs[row].resultStoragePort;
            }
            videoWidgets[row]->setStreamInfo(configTable->item(row, 0)->text(), projectPath, port);
        }
    } else if (column == 2) { // 端口
        QString portText = configTable->item(row, 2)->text();
        bool ok;
        int port = portText.toInt(&ok);

        // 获取当前配置
        QList<StreamConfig> configs = streamManager->getStreamConfigs();
        if (row >= configs.size()) {
            return;
        }

        // 如果端口没有变化，不做处理
        if (ok && port == configs[row].resultStoragePort) {
            return;
        }

        // 检查端口是否有效
        if (ok && checkPort(port)) {
            // 端口有效，更新配置
            qDebug() << QString::fromUtf8("更新视频流") << row << QString::fromUtf8("的端口为:") << port;
            streamManager->updateResultStoragePort(row, port);

            // 更新视频控件
            if (row < videoWidgets.size()) {
                videoWidgets[row]->setStreamInfo(
                    configTable->item(row, 0)->text(),
                    configTable->item(row, 1)->text(),
                    port
                );
            }

            // 如果视频流正在运行，提示用户重启视频流
            if (configs[row].isActive) {
                QMessageBox::information(this, QString::fromUtf8("端口已更新"),
                                      QString::fromUtf8("端口已更新为 %1，但需要重启视频流才能生效").arg(port));
            }
        } else {
            // 端口无效，恢复原值
            // 断开信号连接，避免触发cellChanged
            disconnect(configTable, &QTableWidget::cellChanged, this, &MainWindow::on_configTable_cellChanged);

            // 恢复原值
            configTable->item(row, 2)->setText(QString::number(configs[row].resultStoragePort));

            // 重新连接信号
            connect(configTable, &QTableWidget::cellChanged, this, &MainWindow::on_configTable_cellChanged);

            // 如果端口被占用，提示用户使用自动分配
            if (ok && PortChecker::isPortInUse(port)) {
                // 询问用户是否要自动分配端口
                QMessageBox::StandardButton reply = QMessageBox::question(this,
                    QString::fromUtf8("端口被占用"),
                    QString::fromUtf8("端口 %1 已被占用，是否要自动分配一个可用端口？").arg(port),
                    QMessageBox::Yes | QMessageBox::No);

                if (reply == QMessageBox::Yes) {
                    // 自动分配端口
                    int newPort = findAvailablePort(8888);
                    if (newPort > 0) {
                        // 断开信号连接，避免触发cellChanged
                        disconnect(configTable, &QTableWidget::cellChanged, this, &MainWindow::on_configTable_cellChanged);

                        // 设置新端口
                        configTable->item(row, 2)->setText(QString::number(newPort));

                        // 重新连接信号
                        connect(configTable, &QTableWidget::cellChanged, this, &MainWindow::on_configTable_cellChanged);

                        // 更新配置
                        streamManager->updateResultStoragePort(row, newPort);

                        // 更新视频控件
                        if (row < videoWidgets.size()) {
                            videoWidgets[row]->setStreamInfo(
                                configTable->item(row, 0)->text(),
                                configTable->item(row, 1)->text(),
                                newPort
                            );
                        }

                        QMessageBox::information(this, QString::fromUtf8("端口已分配"),
                                              QString::fromUtf8("已自动分配端口 %1").arg(newPort));
                    }
                }
            }
        }
    }
}

void MainWindow::on_startAllButton_clicked()
{
    // 检查是否有配置的视频流
    QList<StreamConfig> configs = streamManager->getStreamConfigs();
    if (configs.isEmpty()) {
        QMessageBox::warning(this, QString::fromUtf8("启动失败"),
                           QString::fromUtf8("没有配置的视频流，请先添加视频流"));
        return;
    }

    // 检查配置是否完整
    QStringList incompleteStreams;
    for (int i = 0; i < configs.size(); i++) {
        const StreamConfig& config = configs[i];
        if (config.videoPath.isEmpty() || config.projectPath.isEmpty()) {
            incompleteStreams.append(QString::fromUtf8("视频流 #%1").arg(i + 1));
        }
    }

    if (!incompleteStreams.isEmpty()) {
        QString message = QString::fromUtf8("以下视频流配置不完整，请检查视频路径和项目路径：\n");
        message += incompleteStreams.join("\n");
        QMessageBox::warning(this, QString::fromUtf8("启动失败"), message);
        return;
    }

    // 创建进度对话框
    QProgressDialog* progressDialog = createProgressDialog(
        QString::fromUtf8("启动视频流"),
        QString::fromUtf8("正在准备启动所有视频流..."));
    progressDialog->setRange(0, configs.size());
    progressDialog->setValue(0);
    progressDialog->show();
    processEvents();

    // 启动所有视频流
    QElapsedTimer timer;
    timer.start();

    // 逐个启动视频流，并更新进度对话框
    bool allStarted = true;
    int successCount = 0;
    QStringList failedStreams;

    for (int i = 0; i < configs.size(); i++) {
        // 更新进度对话框
        progressDialog->setValue(i);
        progressDialog->setLabelText(QString::fromUtf8("正在启动视频流 #%1 (%2/%3)...")
                                   .arg(i + 1)
                                   .arg(i + 1)
                                   .arg(configs.size()));
        processEvents();

        // 启动当前视频流
        bool success = streamManager->startStream(i);
        if (success) {
            successCount++;
        } else {
            allStarted = false;
            failedStreams.append(QString::fromUtf8("视频流 #%1").arg(i + 1));
        }

        // 处理事件，保持UI响应
        processEvents();
    }

    // 完成进度
    progressDialog->setValue(configs.size());
    progressDialog->setLabelText(QString::fromUtf8("启动完成，成功: %1/%2")
                               .arg(successCount)
                               .arg(configs.size()));
    processEvents();

    qint64 elapsed = timer.elapsed();

    // 短暂延时后关闭进度对话框
    QThread::msleep(500);
    progressDialog->close();
    delete progressDialog;

    // 更新表格
    updateConfigTable();

    if (allStarted) {
        QMessageBox::information(this, QString::fromUtf8("启动成功"),
                               QString::fromUtf8("所有视频流已成功启动\n启动耗时: %1 毫秒")
                               .arg(elapsed));
    } else if (successCount > 0) {
        QString message = QString::fromUtf8("部分视频流启动成功 (%1/%2)\n启动耗时: %3 毫秒\n\n")
                        .arg(successCount)
                        .arg(configs.size())
                        .arg(elapsed);

        message += QString::fromUtf8("以下视频流启动失败：\n");
        message += failedStreams.join("\n");
        message += QString::fromUtf8("\n\n可能的原因：\n"
                                   "1. 视频文件不存在或无法访问\n"
                                   "2. 项目文件不存在或无法访问\n"
                                   "3. 插件加载失败\n"
                                   "4. 视频格式不支持");

        QMessageBox::warning(this, QString::fromUtf8("部分启动成功"), message);
    } else {
        QMessageBox::warning(this, QString::fromUtf8("启动失败"),
                           QString::fromUtf8("所有视频流启动失败\n启动耗时: %1 毫秒\n\n")
                           .arg(elapsed) +
                           QString::fromUtf8("可能的原因：\n"
                           "1. 视频文件不存在或无法访问\n"
                           "2. 项目文件不存在或无法访问\n"
                           "3. 插件加载失败\n"
                           "4. 视频格式不支持"));
    }
}

void MainWindow::on_pauseAllButton_clicked()
{
    // 检查是否有配置的视频流
    QList<StreamConfig> configs = streamManager->getStreamConfigs();
    if (configs.isEmpty()) {
        QMessageBox::warning(this, QString::fromUtf8("暂停失败"),
                           QString::fromUtf8("没有配置的视频流，请先添加视频流"));
        return;
    }

    // 检查是否有正在运行的视频流
    bool hasRunningStream = false;
    for (const StreamConfig& config : configs) {
        if (config.isActive && !config.isPaused) {
            hasRunningStream = true;
            break;
        }
    }

    if (!hasRunningStream) {
        QMessageBox::information(this, QString::fromUtf8("暂停提示"),
                               QString::fromUtf8("没有正在运行的视频流，无需暂停"));
        return;
    }

    // 创建进度对话框
    QProgressDialog* progressDialog = createProgressDialog(
        QString::fromUtf8("暂停视频流"),
        QString::fromUtf8("正在暂停所有视频流..."));
    progressDialog->show();
    processEvents();

    // 暂停所有视频流
    QElapsedTimer timer;
    timer.start();
    bool allPaused = streamManager->pauseAllStreams();
    qint64 elapsed = timer.elapsed();

    // 关闭进度对话框
    progressDialog->close();
    delete progressDialog;

    // 更新表格
    updateConfigTable();

    if (allPaused) {
        QMessageBox::information(this, QString::fromUtf8("暂停成功"),
                               QString::fromUtf8("所有视频流已成功暂停\n暂停耗时: %1 毫秒")
                               .arg(elapsed));
    } else {
        QMessageBox::warning(this, QString::fromUtf8("部分暂停成功"),
                           QString::fromUtf8("部分视频流暂停失败，请检查日志\n暂停耗时: %1 毫秒")
                           .arg(elapsed));
    }
}

void MainWindow::on_stopAllButton_clicked()
{
    // 停止所有视频流
    streamManager->stopAllStreams();

    // 更新表格
    updateConfigTable();
}

void MainWindow::on_layoutComboBox_currentIndexChanged(int index)
{
    // 记录布局变化
    qDebug() << QString::fromUtf8("布局变更为:") << index << QString::fromUtf8("，视频流数量:") << videoWidgets.size();

    // 应用新布局
    applyLayout(index);
}

QProgressDialog* MainWindow::createProgressDialog(const QString& title, const QString& labelText)
{
    QProgressDialog* progressDialog = new QProgressDialog(labelText, "取消", 0, 0, this);
    progressDialog->setWindowTitle(title);
    progressDialog->setWindowModality(Qt::WindowModal);
    progressDialog->setMinimumDuration(0);
    progressDialog->setCancelButton(nullptr); // 禁用取消按钮
    progressDialog->setAutoClose(false);
    progressDialog->setAutoReset(false);
    progressDialog->setMinimumWidth(300);

    return progressDialog;
}

void MainWindow::processEvents()
{
    QApplication::processEvents();
    QThread::msleep(50); // 短暂延时，确保UI更新
    QApplication::processEvents();
}

// 单个视频流控制槽函数
void MainWindow::onStartStream(int streamId)
{
    qDebug() << QString::fromUtf8("开始启动单个视频流:") << streamId;

    // 检查流ID是否有效
    QList<StreamConfig> configs = streamManager->getStreamConfigs();
    if (streamId < 0 || streamId >= configs.size()) {
        QMessageBox::warning(this, QString::fromUtf8("启动失败"),
                           QString::fromUtf8("无效的视频流ID: %1").arg(streamId));
        return;
    }

    // 检查配置是否完整
    const StreamConfig& config = configs[streamId];
    if (config.videoPath.isEmpty() || config.projectPath.isEmpty()) {
        QMessageBox::warning(this, QString::fromUtf8("启动失败"),
                           QString::fromUtf8("视频流配置不完整，请检查视频路径和项目路径"));
        return;
    }

    // 创建进度对话框
    QProgressDialog* progressDialog = createProgressDialog(
        QString::fromUtf8("启动视频流"),
        QString::fromUtf8("正在启动视频流 #%1...").arg(streamId + 1));
    progressDialog->show();
    processEvents();

    // 启动视频流
    QElapsedTimer timer;
    timer.start();
    bool success = streamManager->startStream(streamId);
    qint64 elapsed = timer.elapsed();

    // 关闭进度对话框
    progressDialog->close();
    delete progressDialog;

    // 更新表格
    updateConfigTable();

    if (success) {
        QMessageBox::information(this, QString::fromUtf8("启动成功"),
                               QString::fromUtf8("视频流 #%1 已成功启动\n启动耗时: %2 毫秒")
                               .arg(streamId + 1)
                               .arg(elapsed));
    } else {
        QMessageBox::warning(this, QString::fromUtf8("启动失败"),
                           QString::fromUtf8("无法启动视频流 #%1，请检查配置和日志\n")
                           .arg(streamId + 1) +
                           QString::fromUtf8("可能的原因：\n"
                           "1. 视频文件不存在或无法访问\n"
                           "2. 项目文件不存在或无法访问\n"
                           "3. 插件加载失败\n"
                           "4. 视频格式不支持"));
    }
}

void MainWindow::onPauseStream(int streamId)
{
    qDebug() << QString::fromUtf8("暂停视频流:") << streamId;

    // 检查流ID是否有效
    QList<StreamConfig> configs = streamManager->getStreamConfigs();
    if (streamId < 0 || streamId >= configs.size()) {
        return;
    }

    // 暂停视频流
    streamManager->pauseStream(streamId);

    // 更新表格
    updateConfigTable();
}

void MainWindow::onStopStream(int streamId)
{
    qDebug() << QString::fromUtf8("停止视频流:") << streamId;

    // 检查流ID是否有效
    QList<StreamConfig> configs = streamManager->getStreamConfigs();
    if (streamId < 0 || streamId >= configs.size()) {
        return;
    }

    // 停止视频流
    streamManager->stopStream(streamId);

    // 更新表格
    updateConfigTable();
}

bool MainWindow::checkPort(int port, bool showWarning)
{
    // 检查端口是否有效
    if (port <= 0 || port > 65535) {
        if (showWarning) {
            QMessageBox::warning(this, QString::fromUtf8("端口无效"),
                               QString::fromUtf8("端口必须是1-65535之间的整数"));
        }
        return false;
    }

    // 检查端口是否被占用
    if (PortChecker::isPortInUse(port)) {
        if (showWarning) {
            QMessageBox::warning(this, QString::fromUtf8("端口被占用"),
                               QString::fromUtf8("端口 %1 已被其他程序占用，请选择其他端口").arg(port));
        }
        return false;
    }

    // 检查端口是否被防火墙阻止
    if (PortChecker::isPortBlockedByFirewall(port)) {
        if (showWarning) {
            QMessageBox::warning(this, QString::fromUtf8("端口被阻止"),
                               QString::fromUtf8("端口 %1 可能被防火墙阻止，请检查防火墙设置").arg(port));
        }
        return false;
    }

    return true;
}

int MainWindow::findAvailablePort(int startPort)
{
    // 使用PortChecker查找可用端口
    int port = PortChecker::findAvailablePort(startPort);

    if (port == -1) {
        QMessageBox::warning(this, QString::fromUtf8("无可用端口"),
                           QString::fromUtf8("无法找到可用端口，请关闭一些应用程序后重试"));
    }

    return port;
}
