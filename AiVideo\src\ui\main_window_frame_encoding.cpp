#include "ui/main_window.h"
#include "utils/dialog_utils.h"

#include <QAction>
#include <QComboBox>
#include <QDialog>
#include <QDir>
#include <QFile>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QMenu>
#include <QMenuBar>
#include <QMessageBox>
#include <QProgressDialog>
#include <QPushButton>
#include <QRadioButton>
#include <QSpinBox>
#include <QVBoxLayout>
#include <QDialogButtonBox>
#include <QApplication>

#include <opencv2/opencv.hpp>

namespace ui {

void MainWindow::openFrameEncodingDialog() {
    // 创建对话框
    QDialog dialog(this);
    dialog.setWindowTitle(tr("视频帧编码为视频"));
    dialog.setMinimumWidth(500);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // 添加输入帧目录选择
    QHBoxLayout* inputDirLayout = new QHBoxLayout();
    QLabel* inputDirLabel = new QLabel(tr("输入帧目录:"), &dialog);
    QLineEdit* inputDirEdit = new QLineEdit(&dialog);
    inputDirEdit->setReadOnly(true);
    QPushButton* browseDirButton = new QPushButton(tr("浏览..."), &dialog);
    inputDirLayout->addWidget(inputDirLabel);
    inputDirLayout->addWidget(inputDirEdit);
    inputDirLayout->addWidget(browseDirButton);
    layout->addLayout(inputDirLayout);

    // 添加输出视频文件选择
    QHBoxLayout* outputFileLayout = new QHBoxLayout();
    QLabel* outputFileLabel = new QLabel(tr("输出视频文件:"), &dialog);
    QLineEdit* outputFileEdit = new QLineEdit(&dialog);
    outputFileEdit->setReadOnly(true);
    QPushButton* browseFileButton = new QPushButton(tr("浏览..."), &dialog);
    outputFileLayout->addWidget(outputFileLabel);
    outputFileLayout->addWidget(outputFileEdit);
    outputFileLayout->addWidget(browseFileButton);
    layout->addLayout(outputFileLayout);

    // 添加帧率设置
    QHBoxLayout* fpsLayout = new QHBoxLayout();
    QLabel* fpsLabel = new QLabel(tr("帧率(FPS):"), &dialog);
    QDoubleSpinBox* fpsSpinBox = new QDoubleSpinBox(&dialog);
    fpsSpinBox->setRange(1.0, 120.0);
    fpsSpinBox->setValue(30.0);
    fpsSpinBox->setSingleStep(1.0);
    fpsLayout->addWidget(fpsLabel);
    fpsLayout->addWidget(fpsSpinBox);
    fpsLayout->addStretch();
    layout->addLayout(fpsLayout);

    // 添加编码器选择
    QHBoxLayout* codecLayout = new QHBoxLayout();
    QLabel* codecLabel = new QLabel(tr("编码器:"), &dialog);
    QComboBox* codecComboBox = new QComboBox(&dialog);
    codecComboBox->addItem("H.264", cv::VideoWriter::fourcc('H', '2', '6', '4'));
    codecComboBox->addItem("XVID", cv::VideoWriter::fourcc('X', 'V', 'I', 'D'));
    codecComboBox->addItem("MJPG", cv::VideoWriter::fourcc('M', 'J', 'P', 'G'));
    codecComboBox->addItem("MP4V", cv::VideoWriter::fourcc('M', 'P', '4', 'V'));
    codecLayout->addWidget(codecLabel);
    codecLayout->addWidget(codecComboBox);
    codecLayout->addStretch();
    layout->addLayout(codecLayout);

    // 添加帧文件名模式设置
    QHBoxLayout* patternLayout = new QHBoxLayout();
    QLabel* patternLabel = new QLabel(tr("帧文件名模式:"), &dialog);
    QLineEdit* patternEdit = new QLineEdit("frame_%06d.png", &dialog);
    patternLayout->addWidget(patternLabel);
    patternLayout->addWidget(patternEdit);
    layout->addLayout(patternLayout);

    // 添加说明标签
    QLabel* infoLabel = new QLabel(tr("注意: 文件名模式中的 %06d 将被替换为帧序号。例如: frame_%06d.png 将匹配 frame_000000.png, frame_000001.png 等。"), &dialog);
    infoLabel->setWordWrap(true);
    layout->addWidget(infoLabel);

    // 添加按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
    layout->addWidget(buttonBox);

    // 连接信号
    connect(browseDirButton, &QPushButton::clicked, [&]() {
        QString dir = QFileDialog::getExistingDirectory(&dialog, tr("选择输入帧目录"), 
                                                      recordingOutputPath.isEmpty() ? QDir::homePath() : recordingOutputPath);
        if (!dir.isEmpty()) {
            inputDirEdit->setText(dir);
        }
    });

    connect(browseFileButton, &QPushButton::clicked, [&]() {
        QString file = QFileDialog::getSaveFileName(&dialog, tr("选择输出视频文件"), 
                                                  QDir::homePath(), 
                                                  tr("视频文件 (*.mp4 *.avi)"));
        if (!file.isEmpty()) {
            outputFileEdit->setText(file);
        }
    });

    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        QString inputDir = inputDirEdit->text();
        QString outputFile = outputFileEdit->text();
        double fps = fpsSpinBox->value();
        int codec = codecComboBox->currentData().toInt();
        QString framePattern = patternEdit->text();

        if (inputDir.isEmpty() || outputFile.isEmpty()) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("请选择输入帧目录和输出视频文件！"),
                QMessageBox::Warning);
            return;
        }

        // 执行视频编码
        encodeFramesToVideo(inputDir, outputFile, fps, codec, framePattern);
    }
}

bool MainWindow::encodeFramesToVideo(const QString& inputDir, const QString& outputFile, double fps, int codec, const QString& framePattern) {
    QDir dir(inputDir);
    if (!dir.exists()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("输入目录不存在！"),
            QMessageBox::Warning);
        return false;
    }

    // 创建进度对话框
    QProgressDialog progress(tr("正在编码视频..."), tr("取消"), 0, 100, this);
    progress.setWindowModality(Qt::WindowModal);
    progress.setMinimumDuration(0);
    progress.setValue(0);
    progress.show();
    QApplication::processEvents();

    // 获取帧文件列表

    // QStringList nameFilters;
    // // 将 %d 替换为正则表达式 \d+
    // QString regexPattern = framePattern;
    // regexPattern.replace("%06d", "\\d{6}");
    // nameFilters << regexPattern;
    
    // QStringList frameFiles = dir.entryList(nameFilters, QDir::Files, QDir::Name);
    // if (frameFiles.isEmpty()) {
    //     utils::showScrollableMessageBox(this, tr("错误"),
    //         tr("在目录中没有找到匹配的帧文件！\n请检查帧文件名模式是否正确。"),
    //         QMessageBox::Warning);
    //     return false;
    // }



    QStringList allFiles = dir.entryList(QDir::Files, QDir::Name);
    QString replacedPattern = framePattern;
    replacedPattern.replace("%06d", "\\d{6}");
    QRegularExpression re("^" + replacedPattern + "$");
    QStringList frameFiles;
    for (const QString& file : allFiles) {
        if (re.match(file).hasMatch()) {
            frameFiles << file;
        }
    }



    // 读取第一帧以获取图像尺寸
    cv::Mat firstFrame = cv::imread((inputDir + "/" + frameFiles[0]).toStdString());
    if (firstFrame.empty()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法读取第一帧图像！"),
            QMessageBox::Warning);
        return false;
    }

    // 创建视频写入器
    cv::VideoWriter videoWriter;
    bool isOpened = videoWriter.open(outputFile.toStdString(), codec, fps, firstFrame.size());
    if (!isOpened) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法创建视频文件！\n请检查输出路径和编码器设置。"),
            QMessageBox::Warning);
        return false;
    }

    // 编码视频
    int totalFrames = frameFiles.size();
    int processedFrames = 0;
    bool cancelled = false;

    for (const QString& frameFile : frameFiles) {
        // 检查是否取消
        if (progress.wasCanceled()) {
            cancelled = true;
            break;
        }

        // 更新进度
        int progressValue = (processedFrames * 100) / totalFrames;
        progress.setValue(progressValue);
        progress.setLabelText(tr("正在编码视频... %1/%2").arg(processedFrames).arg(totalFrames));
        QApplication::processEvents();

        // 读取帧
        cv::Mat frame = cv::imread((inputDir + "/" + frameFile).toStdString());
        if (!frame.empty()) {
            // 写入视频
            videoWriter.write(frame);
            processedFrames++;
        }
    }

    // 释放视频写入器
    videoWriter.release();

    // 完成编码
    progress.setValue(100);

    // 显示结果
    if (!cancelled) {
        utils::showScrollableMessageBox(this, tr("编码完成"),
            tr("成功将 %1 帧图像编码为视频文件：\n%2")
                .arg(processedFrames)
                .arg(outputFile),
            QMessageBox::Information);
        return true;
    } else {
        utils::showScrollableMessageBox(this, tr("编码取消"),
            tr("编码操作已取消。\n已处理 %1 帧图像")
                .arg(processedFrames),
            QMessageBox::Information);
        return false;
    }
}

} // namespace ui


