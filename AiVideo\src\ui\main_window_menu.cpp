#include "ui/main_window.h"
#include "utils/dialog_utils.h"
#include "ai/plugins/ui/plugin_management_widget.h"

#include <QAction>
#include <QCheckBox>
#include <QDialog>
#include <QDialogButtonBox>
#include <QFormLayout>
#include <QGroupBox>
#include <QInputDialog>
#include <QLineEdit>
#include <QMenu>
#include <QMenuBar>
#include <QRadioButton>
#include <QSpinBox>
#include <QVBoxLayout>
#include <QHeaderView>

namespace ui {

void MainWindow::setupMenuBar() {
    // 设置窗口无边框
    setWindowFlags(Qt::FramelessWindowHint);

    // 获取主布局
    QVBoxLayout* mainLayout = qobject_cast<QVBoxLayout*>(centralWidget()->layout());
    if (!mainLayout) {
        qWarning("无法获取主布局");
        return;
    }

    // 创建一个容器来包含标题栏和菜单栏
    QWidget* headerContainer = new QWidget(this);
    headerContainer->setObjectName("headerContainer");
    QVBoxLayout* headerLayout = new QVBoxLayout(headerContainer);
    headerLayout->setSpacing(0);
    headerLayout->setContentsMargins(0, 0, 0, 0);

    // 创建标题栏容器
    titleContainer = new QWidget(this);
    titleContainer->setObjectName("titleContainer");
    titleContainer->setFixedHeight(36); // 设置固定高度
    titleContainer->setAutoFillBackground(true); // 确保背景色填充

    // 创建标题栏布局
    QHBoxLayout *titleContainerLayout = new QHBoxLayout(titleContainer);
    titleContainerLayout->setContentsMargins(10, 0, 5, 0); // 减小左侧边距
    titleContainerLayout->setSpacing(3); // 进一步减小控件之间的间距

    // 添加图标和标题
    QLabel *titleIcon = new QLabel(this);
    titleIcon->setPixmap(QIcon(":/rc/icons/file_icon.png").pixmap(24, 24));
    titleIcon->setStyleSheet("background-color: transparent;"); // 确保图标背景透明

    QLabel *titleLabel = new QLabel(tr("阿丘视频AI分析平台VAS"), this);
    titleLabel->setProperty("title", true);

    // 设置标题字体和颜色
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(11);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setStyleSheet("color: white; background-color: transparent;"); // 确保文字背景透明

    titleContainerLayout->addWidget(titleIcon);
    titleContainerLayout->addWidget(titleLabel);
    titleContainerLayout->addStretch();

    // 添加版本信息
    QLabel *versionLabel = new QLabel(tr("V2.3"), this);
    versionLabel->setProperty("version", true);
    versionLabel->setStyleSheet("color: white; background-color: transparent;"); // 确保版本号背景透明
    titleContainerLayout->addWidget(versionLabel);

    // 添加窗口控制按钮
    QHBoxLayout *windowControlLayout = new QHBoxLayout();
    windowControlLayout->setSpacing(8);

    // 最小化按钮
    minimizeButton = new QPushButton("—", this);
    minimizeButton->setObjectName("minimizeButton");
    minimizeButton->setToolTip(tr("最小化"));
    minimizeButton->setFixedSize(40, 28);
    minimizeButton->setFlat(true);
    minimizeButton->setFocusPolicy(Qt::NoFocus);
    minimizeButton->setStyleSheet("color: white; font-size: 12pt; font-weight: bold; background-color: transparent;");

    // 最大化/还原按钮
    maximizeButton = new QPushButton("□", this);
    maximizeButton->setObjectName("maximizeButton");
    maximizeButton->setToolTip(tr("最大化"));
    maximizeButton->setFixedSize(40, 28);
    maximizeButton->setFlat(true);
    maximizeButton->setFocusPolicy(Qt::NoFocus);
    maximizeButton->setStyleSheet("color: white; font-size: 12pt; font-weight: bold; background-color: transparent;");

    // 关闭按钮
    closeButton = new QPushButton("×", this);
    closeButton->setObjectName("closeButton");
    closeButton->setToolTip(tr("关闭"));
    closeButton->setFixedSize(40, 28);
    closeButton->setFlat(true);
    closeButton->setFocusPolicy(Qt::NoFocus);
    closeButton->setStyleSheet("color: white; font-size: 14pt; font-weight: bold; background-color: transparent;");

    // 连接按钮信号
    connect(minimizeButton, &QPushButton::clicked, this, &MainWindow::minimizeWindow);
    connect(maximizeButton, &QPushButton::clicked, this, &MainWindow::toggleMaximizeWindow);
    connect(closeButton, &QPushButton::clicked, this, &MainWindow::closeWindow);

    // 添加按钮到布局
    windowControlLayout->addWidget(minimizeButton);
    windowControlLayout->addWidget(maximizeButton);
    windowControlLayout->addWidget(closeButton);

    // 添加窗口控制按钮到标题容器
    titleContainerLayout->addLayout(windowControlLayout);

    // 创建自定义菜单栏
    QWidget* customMenuBar = new QWidget(this);
    customMenuBar->setObjectName("customMenuBar");
    QHBoxLayout* menuBarLayout = new QHBoxLayout(customMenuBar);
    menuBarLayout->setContentsMargins(5, 0, 5, 0);
    menuBarLayout->setSpacing(0);

    // 创建菜单栏（不使用QMainWindow的默认菜单栏位置）
    QMenuBar *menuBar = new QMenuBar(customMenuBar);
    menuBar->setObjectName("menuBar");
    menuBarLayout->addWidget(menuBar);

    // 保存菜单栏指针，以便后续添加菜单
    menuBar_ = menuBar;

    // 完全禁用原始菜单栏
    QWidget* defaultMenuBar = QMainWindow::menuBar();
    defaultMenuBar->setMaximumHeight(0);
    defaultMenuBar->setMinimumHeight(0);
    defaultMenuBar->hide();

    // 将标题栏和菜单栏添加到头部容器
    headerLayout->addWidget(titleContainer);
    headerLayout->addWidget(customMenuBar);

    // 将头部容器添加到主布局的顶部
    mainLayout->insertWidget(0, headerContainer);

    // 创建文件菜单
    QMenu *fileMenu = menuBar->addMenu(tr("文件"));

    // 项目相关动作
    QAction *newProjectAction = new QAction(tr("新建项目"), this);
    newProjectAction->setShortcut(QKeySequence("Ctrl+N"));
    fileMenu->addAction(newProjectAction);
    connect(newProjectAction, &QAction::triggered, this, &MainWindow::newProject);

    QAction *openProjectAction = new QAction(tr("打开项目"), this);
    openProjectAction->setShortcut(QKeySequence("Ctrl+Alt+O"));
    fileMenu->addAction(openProjectAction);
    connect(openProjectAction, &QAction::triggered, this, &MainWindow::openProject);

    QAction *saveProjectAction = new QAction(tr("保存项目"), this);
    saveProjectAction->setShortcut(QKeySequence("Ctrl+S"));
    fileMenu->addAction(saveProjectAction);
    connect(saveProjectAction, &QAction::triggered, this, &MainWindow::saveProject);

    QAction *saveProjectAsAction = new QAction(tr("项目另存为"), this);
    saveProjectAsAction->setShortcut(QKeySequence("Ctrl+Shift+S"));
    fileMenu->addAction(saveProjectAsAction);
    connect(saveProjectAsAction, &QAction::triggered, this, &MainWindow::saveProjectAs);

    // 最近打开的项目菜单
    recentProjectsMenu = new QMenu(tr("最近打开的项目"), this);
    fileMenu->addMenu(recentProjectsMenu);
    updateRecentProjectsMenu();

    fileMenu->addSeparator();

    // 打开视频动作
    QAction *openVideoAction = new QAction(tr("打开视频"), this);
    openVideoAction->setShortcut(QKeySequence("Ctrl+O"));
    fileMenu->addAction(openVideoAction);
    connect(openVideoAction, &QAction::triggered, this, &MainWindow::browseVideoFile);

    // 打开摄像头动作
    QAction *open_cameraAction = new QAction(tr("打开摄像头"), this);
    open_cameraAction->setShortcut(QKeySequence("Ctrl+C"));
    fileMenu->addAction(open_cameraAction);
    connect(open_cameraAction, &QAction::triggered, this, &MainWindow::open_camera);

    // 打开RTSP流动作
    QAction *openRtspAction = new QAction(tr("打开RTSP流"), this);
    openRtspAction->setShortcut(QKeySequence("Ctrl+R"));
    fileMenu->addAction(openRtspAction);
    connect(openRtspAction, &QAction::triggered, this, &MainWindow::open_rtsp_stream);

    fileMenu->addSeparator();

    // 多路视频流相关动作
    QAction *addStreamAction = new QAction(tr("添加视频流"), this);
    addStreamAction->setShortcut(QKeySequence("Ctrl+A"));
    fileMenu->addAction(addStreamAction);
    connect(addStreamAction, &QAction::triggered, this, &MainWindow::addVideoStream);

    fileMenu->addSeparator();

    // 加载模型动作
    QAction *loadModelAction = new QAction(tr("加载AI模型"), this);
    loadModelAction->setShortcut(QKeySequence("Ctrl+M"));
    fileMenu->addAction(loadModelAction);
    connect(loadModelAction, &QAction::triggered, this, &MainWindow::browseModelFile);

    fileMenu->addSeparator();

    // 退出动作
    QAction *exitAction = new QAction(tr("退出"), this);
    exitAction->setShortcut(QKeySequence("Alt+F4"));
    fileMenu->addAction(exitAction);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);

    // 创建工具菜单
    QMenu *toolsMenu = menuBar->addMenu(tr("工具"));

    // 视频抽帧功能
    QAction *frameExtractionAction = new QAction(tr("视频抽帧"), this);
    toolsMenu->addAction(frameExtractionAction);
    connect(frameExtractionAction, &QAction::triggered, this, &MainWindow::openFrameExtractionDialog);

    // 基于检测结果的视频抽帧功能
    QAction *detectionExtractionAction = new QAction(tr("基于检测结果的视频抽帧"), this);
    toolsMenu->addAction(detectionExtractionAction);
    connect(detectionExtractionAction, &QAction::triggered, this, &MainWindow::openDetectionBasedExtractionDialog);

    // 视频帧编码功能
    QAction *frameEncodingAction = new QAction(tr("视频帧编码为视频"), this);
    toolsMenu->addAction(frameEncodingAction);
    connect(frameEncodingAction, &QAction::triggered, this, &MainWindow::openFrameEncodingDialog);

    // 添加结果查看器选项
    QAction *resultViewerAction = new QAction(tr("结果查看器"), this);
    toolsMenu->addAction(resultViewerAction);
    connect(resultViewerAction, &QAction::triggered, this, &MainWindow::openResultViewer);

    // 创建设置菜单
    QMenu *settingsMenu = menuBar->addMenu(tr("设置"));

    // === 帧处理器配置菜单 ===
    QMenu* frameProcessorMenu = new QMenu(tr("帧处理器配置"), this);
    QAction* usePythonAction = new QAction(tr("使用 Python 帧处理器"), this);
    QAction* useCppAction = new QAction(tr("使用 C++ 帧处理器"), this);
    frameProcessorMenu->addSeparator();
    frameProcessorMenu->addAction(usePythonAction);
    frameProcessorMenu->addAction(useCppAction);
    settingsMenu->addMenu(frameProcessorMenu);
    connect(usePythonAction, &QAction::triggered, [this](){ toggleFrameProcessorType(false); });
    connect(useCppAction, &QAction::triggered, [this](){ toggleFrameProcessorType(true); });

    // === 插件管理、任务管理、修改存储视频地址 ===
    QAction *pluginManageAction = new QAction(tr("插件管理"), this);
    pluginManageAction->setIcon(QIcon(":/rc/plugin_icon.png"));
    settingsMenu->addAction(pluginManageAction);
    connect(pluginManageAction, &QAction::triggered, this, [this]() {
        if (!pluginManager) {
            pluginManager = std::make_shared<ai::plugins::PluginManager>("plugins");
        }
        QDialog dialog(this);
        dialog.setWindowTitle(tr("插件管理"));
        dialog.setMinimumSize(800, 600);
        auto layout = new QVBoxLayout(&dialog);
        auto pluginWidget = new ai::plugins::ui::PluginManagementWidget(pluginManager, &dialog);
        layout->addWidget(pluginWidget);
        dialog.exec();
    });

    QAction *taskManagerAction = new QAction(tr("任务管理"), this);
    settingsMenu->addAction(taskManagerAction);
    connect(taskManagerAction, &QAction::triggered, this, &MainWindow::openTaskManager);

    QAction *modifyVideoOutputPathAction = new QAction(tr("修改存储视频地址"), this);
    settingsMenu->addAction(modifyVideoOutputPathAction);
    connect(modifyVideoOutputPathAction, &QAction::triggered, this, &MainWindow::modifyVideoOutputPath);

    // 设置帧存储路径
    QAction *setFramePathAction = new QAction(tr("设置帧存储路径"), this);
    settingsMenu->addAction(setFramePathAction);
    connect(setFramePathAction, &QAction::triggered, this, &MainWindow::setRecordingPath);

    // 添加设置万物检测大模型参数选项
    QAction *reconfigureModelAction = new QAction(tr("设置万物检测大模型参数"), this);
    settingsMenu->addAction(reconfigureModelAction);
    connect(reconfigureModelAction, &QAction::triggered, this, &MainWindow::reconfigureModel);

    // 添加渲染设置选项
    QAction *renderingSettingsAction = new QAction(tr("渲染设置"), this);
    settingsMenu->addAction(renderingSettingsAction);
    connect(renderingSettingsAction, &QAction::triggered, this, [this]() {
        // 创建渲染设置对话框
        QDialog dialog(this);
        dialog.setWindowTitle(tr("渲染设置"));
        dialog.setMinimumWidth(400);

        QVBoxLayout *layout = new QVBoxLayout(&dialog);

        // 在存储的帧中包含渲染结果
        QCheckBox *includeRenderingCheckBox = new QCheckBox(tr("在存储的帧中包含渲染结果"), &dialog);
        includeRenderingCheckBox->setChecked(includeRenderingInSavedFrames);
        includeRenderingCheckBox->setToolTip(tr("开启后在存储的帧或视频中包含渲染结果"));
        layout->addWidget(includeRenderingCheckBox);

        // 添加按钮
        QDialogButtonBox *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
        layout->addWidget(buttonBox);

        // 连接信号
        connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
        connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

        if (dialog.exec() == QDialog::Accepted) {
            // 保存设置
            includeRenderingInSavedFrames = includeRenderingCheckBox->isChecked();
            saveSettings();
        }
    });

    // 添加结果存储选项
    QAction *resultStorageAction = new QAction(tr("结果存储设置"), this);
    settingsMenu->addAction(resultStorageAction);
    connect(resultStorageAction, &QAction::triggered, this, [this]() {
        // 创建结果存储设置对话框
        QDialog dialog(this);
        dialog.setWindowTitle(tr("结果存储设置"));
        dialog.setMinimumWidth(500);
        dialog.setMinimumHeight(600);

        QVBoxLayout *layout = new QVBoxLayout(&dialog);

        // 启用结果存储选项
        QCheckBox *enableStorageCheckBox = new QCheckBox(tr("启用结果存储"), &dialog);
        enableStorageCheckBox->setChecked(enableResultStorage);
        layout->addWidget(enableStorageCheckBox);

        // 协议类型选择
        QGroupBox *protocolGroupBox = new QGroupBox(tr("协议类型"), &dialog);
        QVBoxLayout *protocolLayout = new QVBoxLayout(protocolGroupBox);

        QComboBox *protocolComboBox = new QComboBox(protocolGroupBox);
        protocolComboBox->addItem(tr("TCP"), static_cast<int>(core::protocols::ProtocolType::TCP));
        protocolComboBox->addItem(tr("Modbus"), static_cast<int>(core::protocols::ProtocolType::MODBUS));
        protocolComboBox->addItem(tr("MQTT"), static_cast<int>(core::protocols::ProtocolType::MQTT));
        protocolComboBox->addItem(tr("自定义"), static_cast<int>(core::protocols::ProtocolType::CUSTOM));

        // 设置当前选中的协议类型
        int currentProtocolIndex = protocolComboBox->findData(static_cast<int>(resultStorageProtocolType));
        if (currentProtocolIndex >= 0) {
            protocolComboBox->setCurrentIndex(currentProtocolIndex);
        }

        protocolLayout->addWidget(protocolComboBox);
        layout->addWidget(protocolGroupBox);

        // 存储模式选择
        QGroupBox *modeGroupBox = new QGroupBox(tr("存储模式"), &dialog);
        QVBoxLayout *modeLayout = new QVBoxLayout(modeGroupBox);

        QRadioButton *immediateRadio = new QRadioButton(tr("即时存储"), modeGroupBox);
        QRadioButton *scheduledRadio = new QRadioButton(tr("定时存储"), modeGroupBox);

        if (resultStorageMode == core::VideoResultStorageServer::StorageMode::IMMEDIATE) {
            immediateRadio->setChecked(true);
        } else {
            scheduledRadio->setChecked(true);
        }

        modeLayout->addWidget(immediateRadio);
        modeLayout->addWidget(scheduledRadio);
        layout->addWidget(modeGroupBox);

        // 刷新间隔设置（仅在定时存储模式下有效）
        QGroupBox *intervalGroupBox = new QGroupBox(tr("刷新间隔（毫秒）"), &dialog);
        QVBoxLayout *intervalLayout = new QVBoxLayout(intervalGroupBox);

        QSpinBox *intervalSpinBox = new QSpinBox(intervalGroupBox);
        intervalSpinBox->setRange(100, 60000);  // 100毫秒到60秒
        intervalSpinBox->setValue(resultStorageFlushInterval);
        intervalSpinBox->setSingleStep(100);
        intervalSpinBox->setEnabled(resultStorageMode == core::VideoResultStorageServer::StorageMode::TIMED);

        intervalLayout->addWidget(intervalSpinBox);
        layout->addWidget(intervalGroupBox);

        // 端口设置
        QGroupBox *portGroupBox = new QGroupBox(tr("服务端口"), &dialog);
        QVBoxLayout *portLayout = new QVBoxLayout(portGroupBox);

        QSpinBox *portSpinBox = new QSpinBox(portGroupBox);
        portSpinBox->setRange(1024, 65535);  // 有效的非系统端口范围
        portSpinBox->setValue(resultStorageTcpPort);
        portSpinBox->setSingleStep(1);

        portLayout->addWidget(portSpinBox);
        layout->addWidget(portGroupBox);

        // Modbus寄存器映射设置（仅在Modbus协议下有效）
        QGroupBox *modbusGroupBox = new QGroupBox(tr("Modbus寄存器映射"), &dialog);
        QVBoxLayout *modbusLayout = new QVBoxLayout(modbusGroupBox);

        QTableWidget *registerTable = new QTableWidget(0, 2, modbusGroupBox);
        registerTable->setHorizontalHeaderLabels({tr("字段名"), tr("寄存器地址")});
        registerTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
        registerTable->setSelectionBehavior(QAbstractItemView::SelectRows);

        // 填充当前的寄存器映射
        int row = 0;
        for (const auto& pair : modbusRegisterMap) {
            registerTable->insertRow(row);
            registerTable->setItem(row, 0, new QTableWidgetItem(QString::fromStdString(pair.first)));
            registerTable->setItem(row, 1, new QTableWidgetItem(QString::number(pair.second)));
            row++;
        }

        // 添加/删除按钮
        QHBoxLayout *registerButtonLayout = new QHBoxLayout();
        QPushButton *addRegisterButton = new QPushButton(tr("添加"), modbusGroupBox);
        QPushButton *removeRegisterButton = new QPushButton(tr("删除"), modbusGroupBox);

        registerButtonLayout->addWidget(addRegisterButton);
        registerButtonLayout->addWidget(removeRegisterButton);

        modbusLayout->addWidget(registerTable);
        modbusLayout->addLayout(registerButtonLayout);
        layout->addWidget(modbusGroupBox);

        // 默认根据当前协议类型显示/隐藏Modbus设置
        modbusGroupBox->setVisible(resultStorageProtocolType == core::protocols::ProtocolType::MODBUS);

        // MQTT配置设置（仅在MQTT协议下有效）
        QGroupBox *mqttGroupBox = new QGroupBox(tr("MQTT配置"), &dialog);
        QFormLayout *mqttLayout = new QFormLayout(mqttGroupBox);

        QLineEdit *brokerAddressEdit = new QLineEdit(mqttBrokerAddress, mqttGroupBox);
        QSpinBox *brokerPortSpinBox = new QSpinBox(mqttGroupBox);
        brokerPortSpinBox->setRange(1, 65535);
        brokerPortSpinBox->setValue(mqttBrokerPort);

        QLineEdit *usernameEdit = new QLineEdit(mqttUsername, mqttGroupBox);
        QLineEdit *passwordEdit = new QLineEdit(mqttPassword, mqttGroupBox);
        passwordEdit->setEchoMode(QLineEdit::Password);

        QLineEdit *topicEdit = new QLineEdit(mqttTopic, mqttGroupBox);
        QSpinBox *qosSpinBox = new QSpinBox(mqttGroupBox);
        qosSpinBox->setRange(0, 2);
        qosSpinBox->setValue(mqttQos);

        mqttLayout->addRow(tr("Broker地址:"), brokerAddressEdit);
        mqttLayout->addRow(tr("Broker端口:"), brokerPortSpinBox);
        mqttLayout->addRow(tr("用户名:"), usernameEdit);
        mqttLayout->addRow(tr("密码:"), passwordEdit);
        mqttLayout->addRow(tr("主题:"), topicEdit);
        mqttLayout->addRow(tr("QoS等级:"), qosSpinBox);

        layout->addWidget(mqttGroupBox);

        // 默认根据当前协议类型显示/隐藏MQTT设置
        mqttGroupBox->setVisible(resultStorageProtocolType == core::protocols::ProtocolType::MQTT);

        // 连接信号
        connect(immediateRadio, &QRadioButton::toggled, [intervalSpinBox](bool checked) {
            intervalSpinBox->setEnabled(!checked);  // 即时存储模式下禁用刷新间隔
        });

        connect(protocolComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [modbusGroupBox, mqttGroupBox, protocolComboBox](int index) {
            // 根据选择的协议类型显示/隐藏Modbus和MQTT设置
            core::protocols::ProtocolType type = static_cast<core::protocols::ProtocolType>(
                protocolComboBox->itemData(index).toInt());
            modbusGroupBox->setVisible(type == core::protocols::ProtocolType::MODBUS);
            mqttGroupBox->setVisible(type == core::protocols::ProtocolType::MQTT);
        });

        connect(addRegisterButton, &QPushButton::clicked, [registerTable]() {
            int row = registerTable->rowCount();
            registerTable->insertRow(row);
            registerTable->setItem(row, 0, new QTableWidgetItem(""));
            registerTable->setItem(row, 1, new QTableWidgetItem("0"));
            registerTable->selectRow(row);
            registerTable->editItem(registerTable->item(row, 0));
        });

        connect(removeRegisterButton, &QPushButton::clicked, [registerTable]() {
            QList<QTableWidgetItem*> selectedItems = registerTable->selectedItems();
            if (!selectedItems.isEmpty()) {
                int row = selectedItems.first()->row();
                registerTable->removeRow(row);
            }
        });

        // 按钮
        QDialogButtonBox *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
        layout->addWidget(buttonBox);

        connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
        connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

        if (dialog.exec() == QDialog::Accepted) {
            // 保存设置
            enableResultStorage = enableStorageCheckBox->isChecked();
            resultStorageMode = immediateRadio->isChecked() ?
                core::VideoResultStorageServer::StorageMode::IMMEDIATE :
                core::VideoResultStorageServer::StorageMode::TIMED;
            resultStorageFlushInterval = intervalSpinBox->value();
            resultStorageTcpPort = portSpinBox->value();
            resultStorageProtocolType = static_cast<core::protocols::ProtocolType>(
                protocolComboBox->currentData().toInt());

            // 保存Modbus寄存器映射
            modbusRegisterMap.clear();
            for (int row = 0; row < registerTable->rowCount(); ++row) {
                QString key = registerTable->item(row, 0)->text();
                QString valueStr = registerTable->item(row, 1)->text();

                if (!key.isEmpty() && !valueStr.isEmpty()) {
                    bool ok;
                    uint16_t value = valueStr.toUShort(&ok);
                    if (ok) {
                        modbusRegisterMap[key.toStdString()] = value;
                    }
                }
            }

            // 保存MQTT配置
            mqttBrokerAddress = brokerAddressEdit->text();
            mqttBrokerPort = brokerPortSpinBox->value();
            mqttUsername = usernameEdit->text();
            mqttPassword = passwordEdit->text();
            mqttTopic = topicEdit->text();
            mqttQos = qosSpinBox->value();

            // 应用设置到视频处理核心
            if (videoProcessingCore) {
                if (enableResultStorage) {
                    bool success = videoProcessingCore->start_result_storage_server(
                        "results",
                        resultStorageMode,
                        resultStorageTcpPort,
                        resultStorageFlushInterval,
                        resultStorageProtocolType
                    );

                    // 如果是Modbus协议，设置寄存器映射
                    if (success && resultStorageProtocolType == core::protocols::ProtocolType::MODBUS) {
                        videoProcessingCore->set_modbus_register_map(modbusRegisterMap);
                    }

                    // 如果是MQTT协议，设置MQTT配置
                    if (success && resultStorageProtocolType == core::protocols::ProtocolType::MQTT) {
                        // TODO: 等待AiVideoCore实现set_mqtt_config方法
                        // videoProcessingCore->set_mqtt_config(
                        //     mqttBrokerAddress.toStdString(),
                        //     mqttBrokerPort,
                        //     mqttUsername.toStdString(),
                        //     mqttPassword.toStdString(),
                        //     mqttTopic.toStdString(),
                        //     mqttQos
                        // );
                    }
                } else {
                    videoProcessingCore->stop_result_storage_server();
                }
            }

            // 保存设置
            saveSettings();
        }
    });

    // 创建设置菜单
    QMenu *runMenu = menuBar->addMenu(tr("系统运行"));
    QAction *launchVideoStreamSystemAction = new QAction(tr("启动多路视频流处理系统"), this);
    runMenu->addAction(launchVideoStreamSystemAction);
    connect(launchVideoStreamSystemAction, &QAction::triggered, this, &MainWindow::launchVideoStreamSystem);

    // 帮助菜单
    QMenu *helpMenu = menuBar->addMenu(tr("帮助"));

    // 关于动作
    QAction *aboutAction = new QAction(tr("关于"), this);
    helpMenu->addAction(aboutAction);
    connect(aboutAction, &QAction::triggered, [this]() {
        utils::showScrollableMessageBox(this, tr("关于阿丘视频平台"),
                                       tr("<h3>阿丘视频AI分析平台VAS</h3>"
                                          "<p>版本: 1.0</p>"
                                          "<p>阿丘科技出品</p>"));
    });
}

} // namespace ui



