#include <QFileDialog>
#include <QMessageBox>
#include "ui/main_window.h"
#include "utils/dialog_utils.h"

namespace ui {

void MainWindow::reloadDllPlugins() {
    // 检查模型是否已加载
    if (!videoProcessingCore->get_model_manager()->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    try {
        // 获取 AI 处理器的 C++ 帧处理器
        auto processor = videoProcessingCore->get_ai_processor()->get_cpp_frame_processor();
        if (!processor) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("C++ 帧处理器未初始化"),
                QMessageBox::Warning);
            return;
        }

        // 询问用户是否要选择自定义目录
        QMessageBox::StandardButton reply = QMessageBox::question(this, tr("重新加载DLL插件"),
            tr("是否要从自定义目录加载DLL插件？\n选择'是'将打开目录选择对话框，选择'否'将使用默认目录。"),
            QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel);

        if (reply == QMessageBox::Cancel) {
            return;
        }

        std::string pluginDir;
        if (reply == QMessageBox::Yes) {
            // 打开目录选择对话框
            QString dir = QFileDialog::getExistingDirectory(
                this,
                tr("选择DLL插件目录"),
                QString(),
                QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks
            );

            if (dir.isEmpty()) {
                return;
            }

            pluginDir = dir.toStdString();
        }

        // 重新加载DLL插件
        int loadedCount = processor->reload_dll_plugins(pluginDir);

        // 更新帧处理器状态标签
        updateFrameProcessorStatus();

        // 显示成功消息
        utils::showScrollableMessageBox(this, tr("成功"),
            tr("已重新加载 %1 个DLL插件。").arg(loadedCount),
            QMessageBox::Information);
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("重新加载DLL插件时出错：%1").arg(e.what()),
            QMessageBox::Warning);
    }
}

} // namespace ui

