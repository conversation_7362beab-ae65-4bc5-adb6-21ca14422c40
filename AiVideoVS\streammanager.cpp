#include "streammanager.h"

#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QDir>
#include <QApplication>
#include <QElapsedTimer>



// StreamProcessingThread 实现

StreamProcessingThread::StreamProcessingThread(int streamId, QObject *parent)
    : QThread(parent), streamId(streamId), running(false), paused(false), videoEnded(false), error(false)
{
}

StreamProcessingThread::~StreamProcessingThread()
{
    // 停止线程
    stopProcessing();

    // 等待线程结束
    wait();
}

void StreamProcessingThread::setConfig(const StreamConfig &config)
{
    QMutexLocker locker(&controlMutex);
    this->config = config;
}

cv::Mat StreamProcessingThread::getCurrentFrame()
{
    QMutexLocker locker(&frameMutex);
    return currentFrame.clone();
}

ai::FrameResult StreamProcessingThread::getCurrentResult()
{
    QMutexLocker locker(&frameMutex);
    return currentResult;
}

void StreamProcessingThread::startProcessing()
{
    QMutexLocker locker(&controlMutex);

    if (!running) {
        running = true;
        paused = false;
        videoEnded = false;
        error = false;

        if (!isRunning()) {
            start();
        } else {
            pauseCondition.wakeAll();
        }
    } else if (paused) {
        paused = false;
        pauseCondition.wakeAll();
    }
}

void StreamProcessingThread::pauseProcessing()
{
    QMutexLocker locker(&controlMutex);
    paused = true;
}

void StreamProcessingThread::stopProcessing()
{
    QMutexLocker locker(&controlMutex);

    if (running) {
        running = false;
        paused = false;
        pauseCondition.wakeAll();
    }
}

bool StreamProcessingThread::isActive() const
{
    QMutexLocker locker(const_cast<QMutex*>(&controlMutex));
    return running;
}

bool StreamProcessingThread::isPaused() const
{
    QMutexLocker locker(const_cast<QMutex*>(&controlMutex));
    return paused;
}

bool StreamProcessingThread::isVideoEnded() const
{
    QMutexLocker locker(const_cast<QMutex*>(&controlMutex));
    return videoEnded;
}

bool StreamProcessingThread::hasError() const
{
    QMutexLocker locker(const_cast<QMutex*>(&controlMutex));
    return error;
}

std::shared_ptr<core::Project> StreamProcessingThread::getProject() const
{
    QMutexLocker locker(const_cast<QMutex*>(&controlMutex));
    return project;
}

std::shared_ptr<core::VideoProcessingCore> StreamProcessingThread::getProcessor() const
{
    QMutexLocker locker(const_cast<QMutex*>(&controlMutex));
    return processor;
}

void StreamProcessingThread::run()
{
    // 初始化
    if (!initialize()) {
        qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("初始化失败");
        {
            QMutexLocker locker(&controlMutex);
            error = true;
            running = false;
        }
        return;
    }

    // 主循环
    while (true) {
        // 检查是否应该继续运行
        {
            QMutexLocker locker(&controlMutex);
            if (!running) {
                break;
            }

            if (paused) {
                pauseCondition.wait(&controlMutex);
                continue;
            }
        }

        // 处理一帧
        if (!processFrame()) {
            QMutexLocker locker(&controlMutex);
            if (!error) {
                videoEnded = true;
            }
            running = false;
            break;
        }

        // 控制帧率
        msleep(30); // 约33fps
    }

    qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("处理线程结束");
}

bool StreamProcessingThread::initialize()
{
    try {
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("开始初始化...");

        // 步骤1: 创建项目管理器
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("步骤1: 创建项目管理器");
        core::ProjectManager& projectManager = core::ProjectManager::get_instance();

        // 步骤2: 打开项目
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("步骤2: 打开项目") << config.projectPath;
        if (!config.projectPath.isEmpty()) {
            // 检查项目文件是否存在
            QFileInfo projectFileInfo(config.projectPath);
            if (!projectFileInfo.exists() || !projectFileInfo.isFile()) {
                qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("项目文件不存在:") << config.projectPath;
                return false;
            }

            project = projectManager.open_project(config.projectPath.toStdString());
            if (!project) {
                qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("无法打开项目:") << config.projectPath;
                return false;
            }

            qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("成功打开项目:") << QString::fromStdString(project->get_name());
        } else {
            qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("项目路径为空");
            return false;
        }

        // 步骤3: 创建视频处理核心
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("步骤3: 创建视频处理核心");
        processor = std::make_shared<core::VideoProcessingCore>();
        if (!processor) {
            qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("无法创建视频处理核心");
            return false;
        }

        // 步骤4: 创建Python脚本管理器
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("步骤4: 创建Python脚本管理器");
        scriptManager = std::make_shared<ai::plugins::PythonScriptManager>("plugins/task");
        if (!scriptManager) {
            qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("无法创建Python脚本管理器");
            return false;
        }

        // 步骤5: 设置项目参数
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("步骤5: 设置项目参数");
        project->set_result_storage_tcp_port(config.resultStoragePort);
        project->set_enable_result_storage(true);  // 启用结果存储服务器
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("结果存储服务器端口设置为:") << config.resultStoragePort;

        // 步骤6: 将项目配置导入到处理核心
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("步骤6: 将项目配置导入到处理核心");
        try {
            // 使用超时机制避免卡死
            QElapsedTimer timer;
            timer.start();

            // 导入项目配置
            bool importSuccess = projectManager.import_to_video_processing_core(project, processor);

            qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("导入项目配置耗时:") << timer.elapsed() << "ms";

            if (!importSuccess) {
                qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("导入项目配置失败");
                return false;
            }
        } catch (const std::exception& e) {
            qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("导入项目配置异常:") << QString::fromUtf8(e.what());
            return false;
        }

        // 步骤7: 打开视频源
        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("步骤7: 打开视频源");
        bool opened = false;

        // 检查视频文件是否存在
        if (!config.videoPath.isEmpty()) {
            // 检查是否是RTSP流
            std::string url = config.videoPath.toStdString();
            if (url.find("rtsp://") == 0) {
                // RTSP流
                qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("尝试打开RTSP流:") << config.videoPath;
                opened = processor->open_rtsp_stream(url);
            } else {
                // 视频文件，检查文件是否存在
                QFileInfo videoFileInfo(config.videoPath);
                if (!videoFileInfo.exists() || !videoFileInfo.isFile()) {
                    qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("视频文件不存在:") << config.videoPath;
                    return false;
                }

                qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("尝试打开视频文件:") << config.videoPath;
                opened = processor->open_video_file(url);
            }

            if (!opened) {
                qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("无法打开视频源:") << config.videoPath;
                return false;
            }
        } else {
            qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("视频路径为空");
            return false;
        }

        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("初始化完成");
        return true;
    } catch (const std::exception& e) {
        qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("初始化异常:") << QString::fromUtf8(e.what());
        return false;
    } catch (...) {
        qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("初始化发生未知异常");
        return false;
    }
}

bool StreamProcessingThread::processFrame()
{
    // 使用超时机制避免处理单帧时卡死
    QElapsedTimer timer;
    timer.start();

    try {
        // 检查处理器是否有效
        if (!processor) {
            qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("处理器为空");
            QMutexLocker locker(&controlMutex);
            error = true;
            return false;
        }

        qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("开始处理下一帧");

        // 使用超时机制处理下一帧
        ai::FrameResult result;

        try {
            // 处理下一帧
            result = processor->process_next_frame();
        } catch (const std::runtime_error& e) {
            // 检查是否是"无法读取下一帧"错误，这通常表示视频结束
            std::string errorMsg = e.what();
            if (errorMsg.find("无法读取下一帧") != std::string::npos) {
                qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("播放结束");
                QMutexLocker locker(&controlMutex);
                videoEnded = true;
                return false;
            } else {
                qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("处理下一帧异常:") << QString::fromUtf8(e.what());
                QMutexLocker locker(&controlMutex);
                error = true;
                return false;
            }
        }

        // 获取处理后的帧
        cv::Mat processedFrame = processor->get_current_raw_frame();

        if (processedFrame.empty()) {
            qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("处理后的帧为空");
            return false;
        }

        // 不在这里渲染处理结果，而是将原始结果传递给VideoStreamWidget
        // 这样可以避免重复渲染

        // 更新当前帧和处理结果
        {
            QMutexLocker locker(&frameMutex);
            currentFrame = processedFrame.clone();
            currentResult = result;
        }

        // 记录处理时间
        qint64 elapsed = timer.elapsed();
        if (elapsed > 100) { // 如果处理时间超过100ms，记录日志
            qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("处理帧耗时:") << elapsed << "ms";
        }

        return true;
    } catch (const std::runtime_error& e) {
        // 检查是否是"无法读取下一帧"错误，这通常表示视频结束
        std::string errorMsg = e.what();
        if (errorMsg.find("无法读取下一帧") != std::string::npos) {
            qDebug() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("播放结束");
            QMutexLocker locker(&controlMutex);
            videoEnded = true;
            return false;
        } else {
            qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("处理异常:") << QString::fromUtf8(e.what());
            QMutexLocker locker(&controlMutex);
            error = true;
            return false;
        }
    } catch (const std::exception& e) {
        qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("处理异常:") << QString::fromUtf8(e.what());
        QMutexLocker locker(&controlMutex);
        error = true;
        return false;
    } catch (...) {
        qWarning() << QString::fromUtf8("视频流") << streamId << QString::fromUtf8("处理时发生未知异常");
        QMutexLocker locker(&controlMutex);
        error = true;
        return false;
    }
}

// StreamManager 实现

StreamManager::StreamManager(QObject *parent) : QObject(parent)
{
    initialize();
}

StreamManager::~StreamManager()
{
    // 停止所有线程
    stopAllStreams();

    // 删除所有线程
    for (auto thread : processingThreads.values()) {
        delete thread;
    }
    processingThreads.clear();
}

void StreamManager::initialize()
{
    // 初始化默认配置
}

void StreamManager::addStream()
{
    QMutexLocker locker(&configMutex);

    // 创建新的流配置
    StreamConfig config;
    config.id = streamConfigs.size();
    config.resultStoragePort = 8888 + config.id;

    // 添加到配置列表
    streamConfigs.append(config);

    // 创建处理线程
    StreamProcessingThread *thread = new StreamProcessingThread(config.id, this);
    thread->setConfig(config);

    // 添加到线程映射
    processingThreads[config.id] = thread;
}

void StreamManager::addStream(const StreamConfig &config)
{
    QMutexLocker locker(&configMutex);

    // 创建新的流配置（复制传入的配置）
    StreamConfig newConfig = config;
    newConfig.id = streamConfigs.size();

    // 添加到配置列表
    streamConfigs.append(newConfig);

    // 创建处理线程
    StreamProcessingThread *thread = new StreamProcessingThread(newConfig.id, this);
    thread->setConfig(newConfig);

    // 添加到线程映射
    processingThreads[newConfig.id] = thread;
}

void StreamManager::removeStream(int index)
{
    QMutexLocker locker(&configMutex);

    if (index < 0 || index >= streamConfigs.size()) {
        return;
    }

    // 停止线程
    if (processingThreads.contains(index)) {
        StreamProcessingThread *thread = processingThreads[index];
        thread->stopProcessing();
        delete thread;
        processingThreads.remove(index);
    }

    // 移除配置
    streamConfigs.removeAt(index);

    // 更新剩余流的ID
    for (int i = 0; i < streamConfigs.size(); i++) {
        streamConfigs[i].id = i;

        // 更新线程ID
        if (processingThreads.contains(i)) {
            processingThreads[i]->setConfig(streamConfigs[i]);
        }
    }
}

QList<StreamConfig> StreamManager::getStreamConfigs() const
{
    QMutexLocker locker(const_cast<QMutex*>(&configMutex));
    return streamConfigs;
}

void StreamManager::updateVideoPath(int index, const QString &path)
{
    QMutexLocker locker(&configMutex);

    if (index < 0 || index >= streamConfigs.size()) {
        return;
    }

    // 更新配置
    streamConfigs[index].videoPath = path;

    // 更新线程配置
    if (processingThreads.contains(index)) {
        processingThreads[index]->setConfig(streamConfigs[index]);
    }
}

void StreamManager::updateProjectPath(int index, const QString &path)
{
    QMutexLocker locker(&configMutex);

    if (index < 0 || index >= streamConfigs.size()) {
        return;
    }

    // 更新配置
    streamConfigs[index].projectPath = path;

    // 更新线程配置
    if (processingThreads.contains(index)) {
        processingThreads[index]->setConfig(streamConfigs[index]);
    }
}

void StreamManager::updateResultStoragePort(int index, int port)
{
    QMutexLocker locker(&configMutex);

    if (index < 0 || index >= streamConfigs.size()) {
        return;
    }

    // 检查端口是否有效
    if (port <= 0 || port > 65535) {
        qWarning() << QString::fromUtf8("视频流") << index << QString::fromUtf8("端口无效:") << port;
        return;
    }

    // 更新配置
    streamConfigs[index].resultStoragePort = port;

    // 更新线程配置
    if (processingThreads.contains(index)) {
        // 获取线程对象
        StreamProcessingThread *thread = processingThreads[index];

        // 更新配置
        thread->setConfig(streamConfigs[index]);

        // 检查线程是否正在运行
        if (thread->isActive()) {
            // 如果线程正在运行，需要重新应用端口设置
            // 获取线程内部的项目和处理器对象
            auto project = thread->getProject();
            auto processor = thread->getProcessor();

            if (project && processor) {
                // 更新项目的端口设置
                project->set_result_storage_tcp_port(port);

                // 停止当前的结果存储服务器
                processor->stop_result_storage_server();

                // 重新启动结果存储服务器
                bool success = processor->start_result_storage_server(
                    "results/stream_" + std::to_string(index),
                    core::VideoResultStorageServer::StorageMode::IMMEDIATE,
                    port,
                    5000
                );

                if (success) {
                    qDebug() << QString::fromUtf8("视频流") << index << QString::fromUtf8("结果存储服务器已重启，新端口:") << port;
                } else {
                    qWarning() << QString::fromUtf8("视频流") << index << QString::fromUtf8("结果存储服务器重启失败");
                }
            }
        }
    }

    qDebug() << QString::fromUtf8("视频流") << index << QString::fromUtf8("端口已更新为:") << port;
}

bool StreamManager::startStream(int index)
{
    QMutexLocker locker(&configMutex);

    if (index < 0 || index >= streamConfigs.size()) {
        return false;
    }

    // 检查配置
    if (streamConfigs[index].videoPath.isEmpty() || streamConfigs[index].projectPath.isEmpty()) {
        qWarning() << "视频流" << index << "配置不完整，无法启动";
        return false;
    }

    // 启动线程
    if (processingThreads.contains(index)) {
        // 获取线程对象
        StreamProcessingThread *thread = processingThreads[index];

        // 在启动线程前，确保结果存储服务器已启动
        auto project = thread->getProject();
        auto processor = thread->getProcessor();

        if (project && processor) {
            // 确保项目的端口设置正确
            project->set_result_storage_tcp_port(streamConfigs[index].resultStoragePort);
            project->set_enable_result_storage(true);

            // 检查结果存储服务器是否已启动
            auto resultServer = processor->get_result_storage_server();
            if (!resultServer || !resultServer->is_running()) {
                // 如果未启动，则启动结果存储服务器
                qDebug() << QString::fromUtf8("视频流") << index << QString::fromUtf8("启动结果存储服务器，端口:") << streamConfigs[index].resultStoragePort;
                bool success = processor->start_result_storage_server(
                    "results/stream_" + std::to_string(index),
                    core::VideoResultStorageServer::StorageMode::IMMEDIATE,
                    streamConfigs[index].resultStoragePort,
                    5000
                );

                if (!success) {
                    qWarning() << QString::fromUtf8("视频流") << index << QString::fromUtf8("结果存储服务器启动失败");
                }
            } else if (resultServer->get_port() != streamConfigs[index].resultStoragePort) {
                // 如果端口不匹配，重启服务器
                qDebug() << QString::fromUtf8("视频流") << index << QString::fromUtf8("重启结果存储服务器，更新端口从")
                         << resultServer->get_port() << QString::fromUtf8("到") << streamConfigs[index].resultStoragePort;

                processor->stop_result_storage_server();
                bool success = processor->start_result_storage_server(
                    "results/stream_" + std::to_string(index),
                    core::VideoResultStorageServer::StorageMode::IMMEDIATE,
                    streamConfigs[index].resultStoragePort,
                    5000
                );

                if (!success) {
                    qWarning() << QString::fromUtf8("视频流") << index << QString::fromUtf8("结果存储服务器重启失败");
                }
            }
        }

        // 启动处理线程
        thread->startProcessing();

        // 更新配置状态
        streamConfigs[index].isActive = true;
        streamConfigs[index].isPaused = false;
        streamConfigs[index].isVideoEnded = false;
        streamConfigs[index].hasError = false;

        return true;
    }

    return false;
}

bool StreamManager::pauseStream(int index)
{
    QMutexLocker locker(&configMutex);

    if (index < 0 || index >= streamConfigs.size()) {
        return false;
    }

    // 暂停线程
    if (processingThreads.contains(index)) {
        processingThreads[index]->pauseProcessing();

        // 更新配置状态
        streamConfigs[index].isPaused = true;

        return true;
    }

    return false;
}

bool StreamManager::stopStream(int index)
{
    QMutexLocker locker(&configMutex);

    if (index < 0 || index >= streamConfigs.size()) {
        return false;
    }

    // 停止线程
    if (processingThreads.contains(index)) {
        processingThreads[index]->stopProcessing();

        // 更新配置状态
        streamConfigs[index].isActive = false;
        streamConfigs[index].isPaused = false;

        return true;
    }

    return false;
}

bool StreamManager::startAllStreams()
{
    // 注意：这里不使用QMutexLocker，因为startStream方法内部已经有锁了
    // 如果这里也加锁，会导致死锁

    bool allStarted = true;

    // 获取流配置数量
    int count = 0;
    {
        QMutexLocker locker(&configMutex);
        count = streamConfigs.size();
    }

    // 逐个启动视频流，并在每次启动后添加延迟
    for (int i = 0; i < count; i++) {
        // 启动当前视频流
        bool success = startStream(i);
        if (!success) {
            allStarted = false;
            qWarning() << QString::fromUtf8("视频流") << i << QString::fromUtf8("启动失败");
        } else {
            qDebug() << QString::fromUtf8("视频流") << i << QString::fromUtf8("启动成功");
        }

        // 添加延迟，避免资源竞争
        QThread::msleep(500); // 500毫秒延迟

        // 处理事件，保持UI响应
        QApplication::processEvents();
    }

    return allStarted;
}

bool StreamManager::pauseAllStreams()
{
    // 注意：这里不使用QMutexLocker，因为pauseStream方法内部已经有锁了
    // 如果这里也加锁，会导致死锁

    bool allPaused = true;

    // 获取流配置数量
    int count = 0;
    {
        QMutexLocker locker(&configMutex);
        count = streamConfigs.size();
    }

    // 逐个暂停视频流
    for (int i = 0; i < count; i++) {
        // 暂停当前视频流
        bool success = pauseStream(i);
        if (!success) {
            allPaused = false;
            qWarning() << QString::fromUtf8("视频流") << i << QString::fromUtf8("暂停失败");
        } else {
            qDebug() << QString::fromUtf8("视频流") << i << QString::fromUtf8("暂停成功");
        }

        // 处理事件，保持UI响应
        QApplication::processEvents();
    }

    return allPaused;
}

bool StreamManager::stopAllStreams()
{
    // 注意：这里不使用QMutexLocker，因为stopStream方法内部已经有锁了
    // 如果这里也加锁，会导致死锁

    bool allStopped = true;

    // 获取流配置数量
    int count = 0;
    {
        QMutexLocker locker(&configMutex);
        count = streamConfigs.size();
    }

    // 逐个停止视频流
    for (int i = 0; i < count; i++) {
        // 停止当前视频流
        bool success = stopStream(i);
        if (!success) {
            allStopped = false;
            qWarning() << QString::fromUtf8("视频流") << i << QString::fromUtf8("停止失败");
        } else {
            qDebug() << QString::fromUtf8("视频流") << i << QString::fromUtf8("停止成功");
        }

        // 处理事件，保持UI响应
        QApplication::processEvents();
    }

    return allStopped;
}

cv::Mat StreamManager::getFrame(int index)
{
    if (index < 0 || index >= streamConfigs.size()) {
        return cv::Mat();
    }

    // 获取线程当前帧
    if (processingThreads.contains(index)) {
        return processingThreads[index]->getCurrentFrame();
    }

    return cv::Mat();
}

ai::FrameResult StreamManager::getResult(int index)
{
    if (index < 0 || index >= streamConfigs.size()) {
        return ai::FrameResult();
    }

    // 获取线程当前处理结果
    if (processingThreads.contains(index)) {
        return processingThreads[index]->getCurrentResult();
    }

    return ai::FrameResult();
}

bool StreamManager::saveConfig(const QString &filePath)
{
    QMutexLocker locker(&configMutex);

    try {
        // 创建JSON对象
        QJsonObject root;

        // 添加流配置数组
        QJsonArray streamsArray;

        for (const StreamConfig &config : streamConfigs) {
            QJsonObject streamObj;
            streamObj["id"] = config.id;
            streamObj["videoPath"] = config.videoPath;
            streamObj["projectPath"] = config.projectPath;
            streamObj["resultStoragePort"] = config.resultStoragePort;

            streamsArray.append(streamObj);
        }

        root["streams"] = streamsArray;

        // 创建JSON文档
        QJsonDocument doc(root);

        // 写入文件
        QFile file(filePath);
        if (!file.open(QIODevice::WriteOnly)) {
            qWarning() << "无法打开文件进行写入:" << filePath;
            return false;
        }

        file.write(doc.toJson());
        file.close();

        return true;
    } catch (const std::exception &e) {
        qWarning() << "保存配置异常:" << e.what();
        return false;
    }
}

bool StreamManager::loadConfig(const QString &filePath)
{
    // 使用UTF-8编码输出日志
    qDebug() << QString::fromUtf8("开始加载配置文件:") << filePath;

    try {
        // 检查文件是否存在
        QFileInfo fileInfo(filePath);
        if (!fileInfo.exists() || !fileInfo.isFile()) {
            qWarning() << QString::fromUtf8("配置文件不存在:") << filePath;
            return false;
        }

        // 打开文件
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly)) {
            qWarning() << QString::fromUtf8("无法打开文件进行读取:") << filePath << QString::fromUtf8(", 错误:") << file.errorString();
            return false;
        }

        // 读取JSON
        QByteArray data = file.readAll();
        file.close();

        if (data.isEmpty()) {
            qWarning() << QString::fromUtf8("配置文件为空:") << filePath;
            return false;
        }

        qDebug() << QString::fromUtf8("成功读取配置文件，大小:") << data.size() << QString::fromUtf8("字节");

        // 解析JSON
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);
        if (parseError.error != QJsonParseError::NoError) {
            qWarning() << QString::fromUtf8("JSON解析错误:") << parseError.errorString() << QString::fromUtf8("，位置:") << parseError.offset;
            return false;
        }

        if (doc.isNull() || !doc.isObject()) {
            qWarning() << QString::fromUtf8("无效的JSON文件格式:") << filePath;
            return false;
        }

        QJsonObject root = doc.object();

        // 停止所有线程 - 在锁外执行，避免死锁
        qDebug() << QString::fromUtf8("停止所有现有视频流");
        stopAllStreams();

        // 处理事件，保持UI响应
        QApplication::processEvents();

        // 现在获取锁，清空配置和线程
        QMutexLocker locker(&configMutex);

        qDebug() << QString::fromUtf8("清空现有配置和线程");
        streamConfigs.clear();
        for (auto thread : processingThreads.values()) {
            delete thread;
        }
        processingThreads.clear();

        // 读取流配置
        if (root.contains("streams") && root["streams"].isArray()) {
            QJsonArray streamsArray = root["streams"].toArray();
            int streamCount = streamsArray.size();

            qDebug() << QString::fromUtf8("发现") << streamCount << QString::fromUtf8("个视频流配置");

            for (int i = 0; i < streamCount; i++) {
                QJsonObject streamObj = streamsArray[i].toObject();

                // 检查必要字段
                if (!streamObj.contains("videoPath") || !streamObj.contains("projectPath")) {
                    qWarning() << QString::fromUtf8("视频流") << i << QString::fromUtf8("配置缺少必要字段");
                    continue;
                }

                QString videoPath = streamObj["videoPath"].toString();
                QString projectPath = streamObj["projectPath"].toString();

                // 检查路径是否有效
                if (videoPath.isEmpty()) {
                    qWarning() << QString::fromUtf8("视频流") << i << QString::fromUtf8("视频路径为空");
                    continue;
                }

                if (projectPath.isEmpty()) {
                    qWarning() << QString::fromUtf8("视频流") << i << QString::fromUtf8("项目路径为空");
                    continue;
                }

                // 创建配置
                StreamConfig config;
                config.id = i;
                config.videoPath = videoPath;
                config.projectPath = projectPath;
                config.resultStoragePort = streamObj["resultStoragePort"].toInt(8888 + i);
                config.isActive = false;
                config.isPaused = false;
                config.isVideoEnded = false;
                config.hasError = false;

                qDebug() << QString::fromUtf8("添加视频流") << i << QString::fromUtf8("配置:");
                qDebug() << QString::fromUtf8("  视频路径:") << config.videoPath;
                qDebug() << QString::fromUtf8("  项目路径:") << config.projectPath;
                qDebug() << QString::fromUtf8("  结果存储端口:") << config.resultStoragePort;

                // 添加到配置列表
                streamConfigs.append(config);

                // 创建处理线程
                StreamProcessingThread *thread = new StreamProcessingThread(config.id, this);
                thread->setConfig(config);

                // 添加到线程映射
                processingThreads[config.id] = thread;

                // 每创建一个线程后处理事件，保持UI响应
                QApplication::processEvents();
            }

            qDebug() << QString::fromUtf8("成功加载") << streamConfigs.size() << QString::fromUtf8("个视频流配置");
        } else {
            qWarning() << QString::fromUtf8("配置文件中没有找到有效的streams数组");
        }

        return !streamConfigs.isEmpty();
    } catch (const std::exception &e) {
        qWarning() << QString::fromUtf8("加载配置异常:") << QString::fromUtf8(e.what());
        return false;
    } catch (...) {
        qWarning() << QString::fromUtf8("加载配置时发生未知异常");
        return false;
    }
}

