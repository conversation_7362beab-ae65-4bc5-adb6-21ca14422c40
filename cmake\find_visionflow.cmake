include(FetchContent)

if(UNIX)
    set(visionflow_SOURCE_DIR /usr/local/visionflow)

    add_library(VisionFlow SHARED IMPORTED)
    set_target_properties(
        VisionFlow
        PROPERTIES
        IMPORTED_LOCATION ${visionflow_SOURCE_DIR}/lib/libvisionflow.so
        INTERFACE_INCLUDE_DIRECTORIES ${visionflow_SOURCE_DIR}/include
        INTERFACE_LINK_LIBRARIES pthread
    )

    add_library(python310 SHARED IMPORTED)
    set_target_properties(
        python310
        PROPERTIES
        IMPORTED_LOCATION ${visionflow_SOURCE_DIR}/lib/libpython310.so
        INTERFACE_INCLUDE_DIRECTORIES ${visionflow_SOURCE_DIR}/include
    )

    add_library(python3 SHARED IMPORTED)
    set_target_properties(
        python3
        PROPERTIES
        IMPORTED_LOCATION ${visionflow_SOURCE_DIR}/lib/libpython3.so
        INTERFACE_INCLUDE_DIRECTORIES ${visionflow_SOURCE_DIR}/include
    )
else()
    # vas盒子版vf版本
    set(lib_resource_path https://pan.aqrose.com/f/e3ffaa0600da4c988bd1/?dl=1)
    # vas平台板vf版本
    # set(lib_resource_path https://pan.aqrose.com/f/f18d6867c5ad44cda74c/?dl=1)
    set(visionflow_SOURCE_DIR ${CMAKE_BINARY_DIR}/visionflow-src)

    add_library(VisionFlow SHARED IMPORTED)
    set_target_properties(
        VisionFlow
        PROPERTIES
        IMPORTED_IMPLIB ${visionflow_SOURCE_DIR}/lib/visionflow.if.lib
        IMPORTED_LOCATION ${visionflow_SOURCE_DIR}/bin/visionflow.dll
        INTERFACE_INCLUDE_DIRECTORIES ${visionflow_SOURCE_DIR}/include
    )

    add_library(python310 SHARED IMPORTED)
    set_target_properties(
        python310
        PROPERTIES
        IMPORTED_IMPLIB ${visionflow_SOURCE_DIR}/lib/python310.lib
        IMPORTED_LOCATION ${visionflow_SOURCE_DIR}/bin/python310.dll
        INTERFACE_INCLUDE_DIRECTORIES ${visionflow_SOURCE_DIR}/include
    )

    add_library(python3 SHARED IMPORTED)
    set_target_properties(
        python3
        PROPERTIES
        IMPORTED_IMPLIB ${visionflow_SOURCE_DIR}/lib/python3.lib
        IMPORTED_LOCATION ${visionflow_SOURCE_DIR}/bin/python3.dll
        INTERFACE_INCLUDE_DIRECTORIES ${visionflow_SOURCE_DIR}/include
    )
endif()

FetchContent_Declare(
    visionflow
    URL ${lib_resource_path}
    SOURCE_DIR ${visionflow_SOURCE_DIR}
    BINARY_DIR ${CMAKE_BINARY_DIR}/visionflow-build
)
FetchContent_MakeAvailable(visionflow)

install(
    DIRECTORY
    ${visionflow_SOURCE_DIR}/bin/
    DESTINATION release
    FILES_MATCHING PATTERN "*.*"
)

