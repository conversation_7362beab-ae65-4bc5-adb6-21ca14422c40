#include "ui/main_window.h"
#include "utils/dialog_utils.h"

#include <QInputDialog>
#include <QFileDialog>
#include <QGridLayout>
#include <QScrollArea>
#include <QMessageBox>

namespace ui {

void MainWindow::addVideoStream()
{
    try {
        // 如果是第一次添加视频流，创建视频流容器
        if (!videoStreamsContainer) {
            try {
                // 创建一个滚动区域来容纳多个视频流
                QScrollArea* scrollArea = new QScrollArea(this);
                if (!scrollArea) {
                    throw std::runtime_error("无法创建滚动区域");
                }

                scrollArea->setWidgetResizable(true);
                scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
                scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

                // 创建视频流容器
                videoStreamsContainer = new QWidget(scrollArea);
                if (!videoStreamsContainer) {
                    throw std::runtime_error("无法创建视频流容器");
                }

                videoStreamsLayout = new QGridLayout(videoStreamsContainer);
                if (!videoStreamsLayout) {
                    throw std::runtime_error("无法创建视频流布局");
                }

                videoStreamsLayout->setSpacing(10);
                videoStreamsContainer->setLayout(videoStreamsLayout);

                scrollArea->setWidget(videoStreamsContainer);

                // 将滚动区域设置为中央部件
                setCentralWidget(scrollArea);

                std::cout << "Created video streams container successfully" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "Exception while creating video streams container: " << e.what() << std::endl;
                utils::showScrollableMessageBox(this, tr("错误"),
                    tr("创建视频流容器失败: %1").arg(e.what()),
                    QMessageBox::Critical);
                return;
            }
        }

        // 创建新的视频流控件
        VideoStreamWidget* streamWidget = nullptr;
        try {
            streamWidget = new VideoStreamWidget(videoStreamsContainer);
            if (!streamWidget) {
                throw std::runtime_error("无法创建视频流控件");
            }

            std::cout << "Created new video stream widget successfully" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Exception while creating video stream widget: " << e.what() << std::endl;
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("创建视频流控件失败: %1").arg(e.what()),
                QMessageBox::Critical);
            return;
        }

        // 设置流名称
        QString streamName = QInputDialog::getText(this, tr("视频流名称"),
                                                 tr("请输入视频流名称:"), QLineEdit::Normal,
                                                 tr("视频流 %1").arg(videoStreams.size() + 1));
        if (streamName.isEmpty()) {
            streamName = tr("视频流 %1").arg(videoStreams.size() + 1);
        }
        streamWidget->setStreamName(streamName);

        // 连接关闭信号
        connect(streamWidget, &VideoStreamWidget::closeStreamRequested, this, &MainWindow::closeVideoStream);

        // 添加到视频流列表
        videoStreams.append(streamWidget);

        // 计算网格位置
        int row = (videoStreams.size() - 1) / 2;
        int col = (videoStreams.size() - 1) % 2;

        // 添加到布局
        videoStreamsLayout->addWidget(streamWidget, row, col);

        // 显示视频流控件
        streamWidget->show();

        // 更新窗口标题
        setWindowTitle(tr("阿丘视频AI分析平台VAS - 多路视频流"));

        std::cout << "Added video stream successfully: " << streamName.toStdString() << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Exception in addVideoStream: " << e.what() << std::endl;
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("添加视频流时发生异常: %1").arg(e.what()),
            QMessageBox::Critical);
    } catch (...) {
        std::cerr << "Unknown exception in addVideoStream" << std::endl;
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("添加视频流时发生未知异常"),
            QMessageBox::Critical);
    }
}

void MainWindow::closeVideoStream(VideoStreamWidget* widget)
{
    try {
        if (!widget) {
            std::cerr << "Warning: Attempted to close null video stream widget" << std::endl;
            return;
        }

        std::cout << "Closing video stream: " << widget->getStreamName().toStdString() << std::endl;

        // 停止视频流的播放
        try {
            widget->stopPlayback();
        } catch (const std::exception& e) {
            std::cerr << "Exception while stopping playback: " << e.what() << std::endl;
            // 继续关闭操作，不返回
        }

        // 从列表中移除
        videoStreams.removeOne(widget);

        // 从布局中移除
        if (videoStreamsLayout) {
            videoStreamsLayout->removeWidget(widget);
        } else {
            std::cerr << "Warning: videoStreamsLayout is null" << std::endl;
        }

        // 删除控件
        widget->deleteLater();

        // 重新排列剩余的视频流
        if (videoStreamsLayout) {
            for (int i = 0; i < videoStreams.size(); ++i) {
                if (videoStreams[i]) {
                    int row = i / 2;
                    int col = i % 2;
                    videoStreamsLayout->addWidget(videoStreams[i], row, col);
                }
            }
        }

        // 如果没有视频流了，恢复原始布局
        if (videoStreams.isEmpty() && videoStreamsContainer) {
            try {
                // 创建一个新的中央部件
                QWidget* centralWidget = new QWidget(this);
                if (!centralWidget) {
                    throw std::runtime_error("无法创建中央部件");
                }

                setCentralWidget(centralWidget);

                // 重置视频流容器
                videoStreamsContainer = nullptr;
                videoStreamsLayout = nullptr;

                // 重新设置原始布局
                QHBoxLayout* mainLayout = new QHBoxLayout(centralWidget);
                if (!mainLayout) {
                    throw std::runtime_error("无法创建主布局");
                }

                mainLayout->setContentsMargins(5, 5, 5, 5);

                // 添加一个提示标签
                QLabel* label = new QLabel(tr("请从菜单中选择'文件 > 添加视频流'来添加视频流"), centralWidget);
                if (!label) {
                    throw std::runtime_error("无法创建提示标签");
                }

                label->setAlignment(Qt::AlignCenter);
                mainLayout->addWidget(label);

                // 更新窗口标题
                setWindowTitle(tr("阿丘视频AI分析平台VAS"));

                std::cout << "Restored original layout" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "Exception while restoring original layout: " << e.what() << std::endl;
                utils::showScrollableMessageBox(this, tr("警告"),
                    tr("恢复原始布局时发生异常: %1").arg(e.what()),
                    QMessageBox::Warning);
            }
        }

        std::cout << "Video stream closed successfully" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Exception in closeVideoStream: " << e.what() << std::endl;
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("关闭视频流时发生异常: %1").arg(e.what()),
            QMessageBox::Critical);
    } catch (...) {
        std::cerr << "Unknown exception in closeVideoStream" << std::endl;
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("关闭视频流时发生未知异常"),
            QMessageBox::Critical);
    }
}

} // namespace ui
