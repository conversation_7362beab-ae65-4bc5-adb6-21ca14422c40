cmake_minimum_required(VERSION 3.19)

project(AiVideo VERSION 1.0.0 LANGUAGES CXX)
set(Qt5_DIR "D:/qt/5.15.0/msvc2019_64/lib/cmake/qt5")

# Find and include Qt5
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)
include_directories(${Qt5Core_INCLUDE_DIRS} ${Qt5Widgets_INCLUDE_DIRS})
link_directories(${Qt5Core_LIBRARY_DIRS} ${Qt5Widgets_LIBRARY_DIRS})
add_definitions(${Qt5Core_DEFINITIONS} ${Qt5Widgets_DEFINITIONS})

set(CMAKE_BUILD_TYPE Release)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
# use only to install pybind11
set(ENV{http_proxy} "http://192.168.0.153:7890")
set(ENV{https_proxy} "http://192.168.0.153:7890")

set(CMAKE_INSTALL_PREFIX ${CMAKE_SOURCE_DIR}/bin)

add_definitions("-DUNICODE" "-D_UNICODE")

add_compile_options(
    /Zi
    /Od
    /utf-8     # 强制使用 UTF-8 编码
    /wd4819    # 禁用 code page 警告
)
add_link_options(/DEBUG)

find_program(LUPDATE_EXECUTABLE "${_qt5_install_prefix}/../../bin/lupdate.exe")
find_program(LRELEASE_EXECUTABLE "${_qt5_install_prefix}/../..//bin/lrelease.exe")

foreach(_ts_file ${TS_FILES})
    execute_process(
        COMMAND ${LUPDATE_EXECUTABLE} -recursive ${CMAKE_SOURCE_DIR} -ts ${_ts_file})
    execute_process(
        COMMAND ${LRELEASE_EXECUTABLE} ${_ts_file})
endforeach()

if(WIN32)
    set(VCPKG_ROOT "D:/vcpkg" CACHE PATH "Vcpkg root directory" FORCE)
else()
    set(VCPKG_ROOT "/home/<USER>/vcpkg" CACHE PATH "Vcpkg root directory" FORCE)
endif()

set(CMAKE_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake" CACHE STRING "Vcpkg toolchain file" FORCE)
include(${CMAKE_TOOLCHAIN_FILE})

# 查找 Python 组件
find_package(Python3 3.12 EXACT COMPONENTS Interpreter Development REQUIRED)
if(NOT Python3_FOUND)
    message(FATAL_ERROR "Python3 not found!")
endif()

include(cmake/find_aivideocore.cmake)
include(cmake/find_visionflow.cmake)
include(cmake/find_pybind11.cmake)


add_subdirectory(AiVideo)
add_subdirectory(AiVideoVS)

# 自动检测并安装vcpkg依赖的DLL
if(WIN32)
    # 获取当前构建配置（Debug/Release等）
    set(VCPKG_BINARY_DIR "${VCPKG_ROOT}/installed/${VCPKG_TARGET_TRIPLET}/bin")
    message(STATUS "VCPKG_BINARY_DIR: ${VCPKG_BINARY_DIR}")
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set(VCPKG_BINARY_DIR "${VCPKG_ROOT}/installed/${VCPKG_TARGET_TRIPLET}/debug/bin")
    endif()
    
    # 查找所有依赖的DLL文件
    file(GLOB_RECURSE DEPENDENCY_DLLS 
        LIST_DIRECTORIES false
        "${VCPKG_BINARY_DIR}/*.dll"
    )
    message(STATUS "Found vcpkg dependencies: ${DEPENDENCY_DLLS}")
    
    # 将DLL安装到bin目录
    install(
        FILES ${DEPENDENCY_DLLS}
        DESTINATION release
        CONFIGURATIONS ${CMAKE_BUILD_TYPE}
    )
endif()


