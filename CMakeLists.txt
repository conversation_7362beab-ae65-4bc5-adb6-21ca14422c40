cmake_minimum_required(VERSION 3.19)

project(AiVideo VERSION 1.0.0 LANGUAGES CXX)
set(Qt5_DIR "D:/qt/5.15.0/msvc2019_64/lib/cmake/qt5")

# Find and include Qt5
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)
include_directories(${Qt5Core_INCLUDE_DIRS} ${Qt5Widgets_INCLUDE_DIRS})
link_directories(${Qt5Core_LIBRARY_DIRS} ${Qt5Widgets_LIBRARY_DIRS})
add_definitions(${Qt5Core_DEFINITIONS} ${Qt5Widgets_DEFINITIONS})

set(CMAKE_BUILD_TYPE Release)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
# use only to install pybind11
# set(ENV{http_proxy} "http://192.168.0.153:7890")
# set(ENV{https_proxy} "http://192.168.0.153:7890")

set(CMAKE_INSTALL_PREFIX ${CMAKE_SOURCE_DIR}/bin)

add_definitions("-DUNICODE" "-D_UNICODE")

add_compile_options(
    /Zi
    /Od
    /utf-8     # 强制使用 UTF-8 编码
    /wd4819    # 禁用 code page 警告
)
add_link_options(/DEBUG)

# 为静态链接设置运行时库
if(WIN32)
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
endif()

find_program(LUPDATE_EXECUTABLE "${_qt5_install_prefix}/../../bin/lupdate.exe")
find_program(LRELEASE_EXECUTABLE "${_qt5_install_prefix}/../..//bin/lrelease.exe")

foreach(_ts_file ${TS_FILES})
    execute_process(
        COMMAND ${LUPDATE_EXECUTABLE} -recursive ${CMAKE_SOURCE_DIR} -ts ${_ts_file})
    execute_process(
        COMMAND ${LRELEASE_EXECUTABLE} ${_ts_file})
endforeach()

if(WIN32)
    set(VCPKG_ROOT "D:/vcpkg" CACHE PATH "Vcpkg root directory" FORCE)
    # 设置使用静态库triplet
    set(VCPKG_TARGET_TRIPLET "x64-windows-static" CACHE STRING "Vcpkg target triplet" FORCE)
else()
    set(VCPKG_ROOT "/home/<USER>/vcpkg" CACHE PATH "Vcpkg root directory" FORCE)
    set(VCPKG_TARGET_TRIPLET "x64-linux" CACHE STRING "Vcpkg target triplet" FORCE)
endif()

set(CMAKE_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake" CACHE STRING "Vcpkg toolchain file" FORCE)
include(${CMAKE_TOOLCHAIN_FILE})

# 查找 Python 组件
find_package(Python3 3.12 EXACT COMPONENTS Interpreter Development REQUIRED)
if(NOT Python3_FOUND)
    message(FATAL_ERROR "Python3 not found!")
endif()

include(cmake/find_aivideocore.cmake)
include(cmake/find_visionflow.cmake)
include(cmake/find_pybind11.cmake)


add_subdirectory(AiVideo)
add_subdirectory(AiVideoVS)

# 注意：使用静态库triplet (x64-windows-static) 后，不再需要安装DLL
# 因为所有依赖都会静态链接到可执行文件中


