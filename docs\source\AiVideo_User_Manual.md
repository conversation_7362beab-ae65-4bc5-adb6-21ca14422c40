# AiVideo 操作手册

## 系统概述
视频分析系统软件AiVideo 1.0（以下简称AiVideo）的主要作用是对工业生产场景中的视频数据内容进行分析。分析的内容主要包括工业生产中的操作流程、异常检测等。用户根据生产场景的特点，选择相应的模型与工具，利用AiVideo进行实时分析并根据分析结果进行预警等操作。

AiVideo作为独立运行的软件，需要安装在执行视频分析任务的计算机中。通过图形界面进行操作。

## 系统价值
- 提供便捷工具方便客户快速搭建视频分析系统平台。
- 内置任务模型训练软件，方便进行模型训练和管理。
- 内置插件功能，快捷切换不同视频分析任务类型。
- 帮助客户对生产流程进行自动化监管，提高生产流程的准确率和合格率。
- 实时对生成过程中的异常进行告警，提高安全生产效率，降低危险发生频次。

## 系统的主要功能点
- 连接监控相机等视频输入设备，实时获取视频数据。
- 对视频数据进行编解码。
- 提供模型训练工具，使用解码后的视频帧训练任务模型。
- 使用任务模型对视频数据或视频流数据进行实时检测，并显示检测信息。
- 提供插件管理功能，内置通用任务插件，实现对模型检测结果的实时分析与告警。
## 系统的运行环境
### 服务器配置
- CPU：Intel i7系列处理器或更高
- 内存：32B或更高
- 存储：512G SSD+1T企业级HDD
- 网卡：千兆网卡网口数量4个或更多（推荐支持PoE）
- 操作系统：Windows 10系统或更高
### 开发语言
- 主程序：C++
- 插件：C++/Python
## 系统主要界面
### 软件主界面
打开软件后展示的首个界面。

![软件主界面](images/Main_Interface.png)

### 多路视频处理界面
从主界面的菜单中打开多路视频处理界面，可以看到多路视频实时处理画面。

![多路视频处理界面](images/Multi_Video_Processing_Interface.png)

### 插件管理界面
对任务分析插件进行参数配置、启用禁用等操作管理。

![插件管理界面1](images/Plugin_Management_Interface1.png)

![插件管理界面2](images/Plugin_Management_Interface2.png)

## 系统操作流程
### 打开软件
软件图标如下：

![软件图标](images/Software_Icon.png)

双击软件图标打开AiVideo。

![软件主界面](images/Main_Interface.png)

### 加载视频
点击主界面中“视频文件”一栏右侧的“浏览”按钮，选择视频文件。

![加载视频](images/Load_Video.png)

### 视频抽帧
选择菜单栏中的设置->设置帧存储路径。

![帧存储路径](images/Set_Frame_Storage_Path.png)

![帧存储路径2](images/Set_Storage_Path2.png)

选择菜单栏中的工具->视频抽帧，打开视频抽帧对话框。

![视频抽帧](images/Video_Frame_Extraction.png)

![视频抽帧](images/Video_Frame_Extraction_Settings.png)

点击OK开始进行视频抽帧。抽帧生成的图像保存在设置好的视频帧存储路径内。
### 打开算法平台
选择菜单栏中的工具->打开算法平台。

![打开算法平台](images/Open_Algorithm_Platform.png)

自动打开算法平台工具。

![AIDI界面](images/AIDI_Interface.png)

使用算法工具平台，根据抽帧生成的图像数据，训练任务模型并导出任务模型（算法工具平台使用方法参考AIDI使用手册）。

![AIDI训练界面](images/AIDI_Training_Interface.png)

### 加载模型
点击在AiVideo主界面中“AI模型文件”一栏右侧的“浏览”按钮，选择上一步导出的模型文件。

![加载模型](images/Load_Model.png)

### 选择任务插件
在AiVideo主界面右侧“任务处理”一栏中，点击“任务管理”按钮，弹出任务管理选择对话框。

![任务管理](images/Task_Management.png)

点击所需进行的视频分析任务，如行为监控、计数等。点击“配置”按钮配置相关参数。

![配置插件参数](images/Configure_Plugin_Parameters.png)

点击“重置”按钮重置算法状态。点击“保存”按钮保存配置，并关闭该对话

![重置算法状态](images/Reset_Algorithm_Status.png)

点击“启用/禁用”按钮开启/关闭该视频分析功能。点击“关闭”按钮退出任务管理对话框。

![启用插件](images/Enable_Plugin.png)

### 实时预览视频分析结果
在AiVideo主界面右侧的“任务处理”一栏的“处理模式”中，选择“AI任务处理”。
在AiVideo主界面右侧的“AI处理设置”一栏的“输出节点ID”中，输入对应的算法模块名，如“检测”、“分割”等。修改完成后，点击下方的“应用节点设置”应用修改。

![AI任务处理](images/AI_Task_Processing.png)

在AiVideo主界面视频画面下方，勾选“包含渲染结果”，然后点击“播放”按钮，即可查看到视频分析结果。

![播放渲染视频](images/Play_Rendered_Video.png)

### 工程管理
对于配置好的视频分析任务，可以保存为项目，以便随时打开访问。保存方式为选择菜单栏中的文件->保存项目选项。菜单栏中，还提供新建项目、打开项目、项目另存为、最近打开的项目等功能。

![工程管理](images/Project_Management.png)

### 多路视频查看
选择菜单栏中的系统运行->启动多路视频流处理系统，打开多路视频处理系统AiVideoVS工具。

![打开多路视频查看](images/Open_Multi_Stream_View.png)

点击“添加视频流”按钮添加视频，重复点击可添加多路视频流。在视频流配置一栏中，依次填入视频路径和项目路径，填入后，点击“全部启动”按钮，即开始实时对多路视频任务进行查看。

![添加多路视频](images/Add_Multi_Stream.png)

也可通过右侧的“浏览视频”与“浏览项目”按钮进行视频流与项目的添加。视频路径可以为本地视频文件路径，也可以为RTSP视频流。

![添加多路视频](images/Add_Multi_Stream_Browse.png)

在“添加视频流”按钮下方，点击“单个视频”选择框即可更改显示布局。

![多路视频布局](images/Multi_Stream_Layout.png)
