
# 确保这些设置在项目定义之后，在添加可执行文件之前
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Qt5 包
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)

# 收集其他源文件
file(GLOB_RECURSE VIDEO_AI_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/main.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*/*/*.cpp"
)

# 收集头文件
file(GLOB_RECURSE VIDEO_AI_HEADERS
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*/*.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*/*/*.h"
)

# 添加资源文件
list(APPEND VIDEO_AI_SOURCES "app.rc" "resources/resources.qrc")

if(WIN32)
    set(APP_ICON_RESOURCE_WINDOWS "${CMAKE_CURRENT_SOURCE_DIR}/app.rc")
    add_executable(AiVideo ${VIDEO_AI_SOURCES} ${VIDEO_AI_HEADERS} ${APP_ICON_RESOURCE_WINDOWS})
else()
    add_executable(AiVideo ${VIDEO_AI_SOURCES} ${VIDEO_AI_HEADERS})
endif()

target_include_directories(
    AiVideo
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${AIVIDEOCORE_INCLUDE_DIRS}
)

target_compile_definitions(AiVideo PRIVATE VFLOW_ENABLE_OPENCV NON_BLOCKING_MODE WIN32_LEAN_AND_MEAN)

# 设置 CMake 的编码
if(MSVC)
    # 为 MSVC 编译器设置 UTF-8 编码
    add_compile_options(
        /utf-8                   # 强制使用 UTF-8
        /wd4819                  # 禁用 code page 警告
        /DWIN32_LEAN_AND_MEAN   # 减少 Windows 头文件包含
    )

    # 添加 Unicode 定义
    add_compile_definitions(
        _UNICODE
        UNICODE
        NOMINMAX                 # 避免 Windows 宏与 STL 冲突
    )
endif()

# 确保源文件使用 UTF-8 编码
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(-finput-charset=UTF-8)
endif()

if(MSVC)
    add_compile_definitions(
        _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING
        HAVE_SNPRINTF
    )
endif()


# Ensure Python definitions are set correctly
add_compile_definitions(
    PYTHON_EXECUTABLE="${Python3_EXECUTABLE}"
    PYTHON_INCLUDE_DIR="${Python3_INCLUDE_DIRS}"
    PYTHON_LIBRARY="${Python3_LIBRARIES}"
)

target_link_libraries(AiVideo PRIVATE
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Gui
    VisionFlow
    pybind11::embed
    Python3::Python
    ${AIVIDEOCORE_LIBRARIES}
)

# 链接 OpenCV 库
if(WIN32)
    target_link_libraries(AiVideo
        PUBLIC
        Opencv
    )
else()
    find_package(OpenCV CONFIG REQUIRED)
    target_link_libraries(AiVideo PUBLIC ${OpenCV_LIBS})
endif()

# 链接 Boost 库
find_package(boost_filesystem CONFIG REQUIRED)
target_link_libraries(AiVideo PUBLIC Boost::filesystem)
find_package(boost_log CONFIG REQUIRED)
target_link_libraries(AiVideo PUBLIC Boost::log)

# 链接 jsoncpp 库
find_package(jsoncpp CONFIG REQUIRED)
target_link_libraries(AiVideo PUBLIC JsonCpp::JsonCpp)

# 安装目标
install(
    TARGETS AiVideo
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION release
    LIBRARY DESTINATION lib
)

# 创建脚本目录
install(
    DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/scripts/
    DESTINATION release/scripts
    FILES_MATCHING PATTERN "*.py"
)


















