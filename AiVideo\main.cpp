﻿#include <qobject.h>
#include <visionflow/core/entry.hpp>
#include <QApplication>
#include <QMainWindow>
#include <QLabel>
#include <QVBoxLayout>
#include <QPushButton>
#include <QMessageBox>
#include <QTextCodec>
#include <QLocale>
#include <QObject>
#include <QFile>
#include <QDir>
#include <QStyleFactory>
#include <QDebug>
#include "include/ui/main_window.h"
#include "utils/settings_manager.h"
#include "utils/log_manager.h"

#ifdef _WIN32
#include <windows.h>
#endif

int main(int argc, char *argv[]) {

    // 由于使用WIN32应用程序，不再需要设置控制台编码
    //设置编码为utf8
    #ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    #endif


    utils::LogManager::get_instance().initialize("Project",
        utils::LogLevel::Info,
        utils::LogLevel::Info);
    // 在创建 QApplication 之前设置属性
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);

    // 设置默认编码为UTF-8
    #if (QT_VERSION < QT_VERSION_CHECK(6, 0, 0))
        QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    #endif

    QApplication app(argc, argv);

    // 注册TaskPlugin智能指针类型到Qt元对象系统
    qRegisterMetaType<std::shared_ptr<ai::plugins::TaskPlugin>>("std::shared_ptr<ai::plugins::TaskPlugin>");

    // 设置本地化
    QLocale::setDefault(QLocale(QLocale::Chinese, QLocale::China));

    // 设置应用程序信息
    QString appName = QObject::tr("阿丘视频平台");
    QString orgName = QObject::tr("阿丘科技");
    QString orgDomain = QObject::tr("aqrose");

    QCoreApplication::setApplicationName(appName);
    QCoreApplication::setOrganizationName(orgName);
    QCoreApplication::setOrganizationDomain(orgDomain);

    // 初始化设置管理器，将配置从注册表移动到AppData目录
    utils::SettingsManager::get_instance().initialize(orgName.toStdString(), appName.toStdString());

    // 创建插件目录
    QDir pluginsDir(QCoreApplication::applicationDirPath() + "/plugins/frame_processors");
    if (!pluginsDir.exists()) {
        pluginsDir.mkpath(".");
    }

    // 设置应用程序默认字体
    QFont font("Microsoft YaHei");  // 使用微软雅黑字体
    font.setPixelSize(12);
    QApplication::setFont(font);

    // 应用高科技黑色主题
    // 从资源文件加载样式表
    QFile styleFile(":/rc/style.qss");
    if (styleFile.open(QFile::ReadOnly)) {
        QString styleSheet = QLatin1String(styleFile.readAll());
        app.setStyleSheet(styleSheet);
        styleFile.close();
        qDebug() << "已加载自定义样式表";
    } else {
        qWarning() << "无法加载样式表:" << styleFile.errorString();

        // 如果无法加载样式表文件，则使用内联样式表作为备用
        QString styleSheet = R"(
            /* 全局样式 */
            * {
                font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
                color: #dfe6e9;
                background-color: #15202b;
            }

            /* 按钮样式 */
            QPushButton {
                background-color: #2980b9;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                min-width: 80px;
                outline: none;
                font-size: 9pt;
            }

            QPushButton:hover {
                background-color: #3498db;
            }

            QPushButton:pressed {
                background-color: #1a5c8a;
            }

            QPushButton:disabled {
                background-color: #192734;
                color: #38444d;
            }

            /* 菜单栏样式 */
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #15202b,
                                           stop:1 #192734);
                color: #dfe6e9;
                border-bottom: 1px solid #38444d;
                padding: 4px;
                min-height: 24px;
            }

            /* 头部容器样式 */
            QWidget#headerContainer {
                background-color: transparent;
            }

            /* 自定义菜单栏容器 */
            QWidget#customMenuBar {
                background-color: transparent;
            }

            /* 隐藏默认菜单栏 */
            QMainWindow > QMenuBar:not(#menuBar) {
                max-height: 0px;
                min-height: 0px;
                color: transparent;
                background-color: transparent;
                border: none;
            }

            QMenuBar::item {
                background: transparent;
                padding: 6px 10px;
                margin: 0px;
                color: #dfe6e9;
                border-radius: 4px;
            }

            QMenuBar::item:selected {
                background: rgba(0, 168, 255, 0.2);
            }

            QMenuBar::item:pressed {
                background: rgba(0, 168, 255, 0.3);
            }

            QMenu {
                background-color: #192734;
                border: 1px solid #38444d;
                border-radius: 6px;
                padding: 5px 0px;
            }

            QMenu::item {
                padding: 8px 25px 8px 20px;
                color: #dfe6e9;
            }

            QMenu::item:selected {
                background: rgba(0, 168, 255, 0.2);
            }

            /* 标题栏样式 */
            QWidget#titleContainer {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #192734,
                                           stop:1 #15202b);
                min-height: 36px;
                max-height: 36px;
                padding: 0px;
            }

            #titleContainer QLabel {
                color: #dfe6e9;
                font-size: 10pt;
                font-weight: bold;
                background-color: transparent;
            }

            /* 窗口控制按钮样式 */
            #minimizeButton, #maximizeButton, #closeButton {
                background: transparent;
                border: none;
                border-radius: 0;
                min-width: 40px;
                max-width: 40px;
                min-height: 28px;
                max-height: 28px;
                padding: 0;
                margin: 0;
                font-family: "Segoe UI", "Microsoft YaHei";
                font-size: 10pt;
                color: #dfe6e9;
            }

            #minimizeButton:hover, #maximizeButton:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            #minimizeButton:pressed, #maximizeButton:pressed {
                background: rgba(255, 255, 255, 0.2);
            }

            #closeButton:hover {
                background: rgba(255, 0, 0, 0.7);
            }

            #closeButton:pressed {
                background: rgb(255, 0, 0);
            }

            /* 次要按钮样式 */
            QPushButton[class="secondary"] {
                background-color: #192734;
                color: #00a8ff;
                border: 1px solid #00a8ff;
            }

            QPushButton[class="secondary"]:hover {
                background-color: rgba(0, 168, 255, 0.1);
            }

            QPushButton[class="secondary"]:pressed {
                background-color: rgba(0, 168, 255, 0.2);
            }
        )";

        app.setStyleSheet(styleSheet);
    }

    // 设置应用程序风格为 Fusion (更现代的扁平化风格)
    app.setStyle(QStyleFactory::create("Fusion"));

    auto mainWindow = ui::MainWindow();
    mainWindow.show();

    return app.exec();
}




