# JsonCpp静态链接修改说明

## 修改概述

本次修改将videoai项目中的jsoncpp依赖从动态库改为静态库，以避免运行时对jsoncpp DLL的依赖。

## 具体修改

### 1. CMakeLists.txt 主要修改

#### 设置vcpkg使用静态库triplet
```cmake
if(WIN32)
    set(VCPKG_ROOT "D:/vcpkg" CACHE PATH "Vcpkg root directory" FORCE)
    # 设置使用静态库triplet
    set(VCPKG_TARGET_TRIPLET "x64-windows-static" CACHE STRING "Vcpkg target triplet" FORCE)
else()
    set(VCPKG_ROOT "/home/<USER>/vcpkg" CACHE PATH "Vcpkg root directory" FORCE)
    set(VCPKG_TARGET_TRIPLET "x64-linux" CACHE STRING "Vcpkg target triplet" FORCE)
endif()
```

#### 设置静态链接运行时库
```cmake
# 为静态链接设置运行时库
if(WIN32)
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
endif()
```

#### 移除DLL安装逻辑
移除了原来自动检测并安装vcpkg依赖DLL的代码，因为使用静态库后不再需要DLL。

### 2. 依赖变化

- **之前**: 使用 `x64-windows` triplet，生成动态库依赖
- **现在**: 使用 `x64-windows-static` triplet，生成静态库依赖

### 3. 文件结构变化

#### 静态库版本 (x64-windows-static)
```
build/vcpkg_installed/x64-windows-static/
├── lib/
│   ├── jsoncpp.lib          # 静态库文件
│   └── boost_*.lib          # 其他静态库
├── include/
│   └── json/                # 头文件
└── share/
    └── jsoncpp/             # CMake配置文件
```

#### 动态库版本 (x64-windows) - 已不使用
```
build/vcpkg_installed/x64-windows/
├── bin/
│   └── *.dll                # DLL文件
├── lib/
│   └── *.lib                # 导入库
└── include/
    └── json/                # 头文件
```

## 优势

1. **无DLL依赖**: 运行时不再需要jsoncpp.dll文件
2. **部署简化**: 只需要可执行文件，无需额外的DLL文件
3. **版本一致性**: 避免了DLL版本冲突问题

## 使用方法

1. 清理构建目录（可选，但推荐）:
   ```bash
   rm -rf build/
   ```

2. 重新配置和构建:
   ```bash
   mkdir build
   cd build
   cmake ..
   cmake --build . --config Release
   ```

3. 在VSCode中使用CMake插件:
   - 删除build目录
   - 使用CMake插件的"Configure"功能
   - 使用CMake插件的"Build"功能

## 注意事项

1. 首次构建时，vcpkg会下载并编译静态版本的所有依赖，可能需要较长时间
2. 生成的可执行文件会比动态链接版本稍大，因为包含了所有依赖的代码
3. 如果需要切换回动态库，将`VCPKG_TARGET_TRIPLET`改回`x64-windows`即可

## 验证方法

构建完成后，可以通过以下方式验证静态链接是否成功：

1. 检查生成的可执行文件不依赖jsoncpp.dll
2. 运行程序时不需要额外的DLL文件
3. 可以将可执行文件复制到其他机器上运行（只要有相同的运行时环境）
