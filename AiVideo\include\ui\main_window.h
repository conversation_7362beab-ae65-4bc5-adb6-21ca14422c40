﻿#pragma once

#include <QButtonGroup>
#include <QCheckBox>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QMainWindow>
#include <QPushButton>
#include <QRadioButton>
#include <QResizeEvent>
#include <QSettings>
#include <QSlider>
#include <QTimer>
#include <QMouseEvent>
#include <QPoint>

#include <memory>

#include "core/video_processing_core.h"
#include "fullscreen_window.h"
#include "ui/result_viewer_dialog.h"
#include "ui/task_manager_dialog.h"
#include "utils/q_plugin_renderer.h"
#include "ui/video_stream_widget.h"
#include "ui/cpp_frame_processor_dialog.h"
#include "ai/plugins/plugin_manager.h"
#include "ai/plugins/python_script_manager.h"


namespace ui {

/**
 * @brief 主窗口类
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    MainWindow(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~MainWindow();

signals:
    // 移除不再需要的信号
    // void restrictedAreaUpdated(const std::vector<cv::Point>& area);
    // void monitoringAreaUpdated(const std::vector<cv::Point>& area);

protected:
    /**
     * @brief 窗口大小变化事件处理
     * @param event 大小变化事件
     */
    void resizeEvent(QResizeEvent* event) override;

    /**
     * @brief 鼠标按下事件处理
     * @param event 鼠标事件
     */
    void mousePressEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标移动事件处理
     * @param event 鼠标事件
     */
    void mouseMoveEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标释放事件处理
     * @param event 鼠标事件
     */
    void mouseReleaseEvent(QMouseEvent* event) override;

private slots:
    /**
     * @brief 创建新项目
     */
    void newProject();

    /**
     * @brief 打开项目
     */
    void openProject();

    /**
     * @brief 保存项目
     */
    void saveProject();

    /**
     * @brief 另存为项目
     */
    void saveProjectAs();

    /**
     * @brief 打开最近的项目
     * @param filePath 项目文件路径
     */
    void openRecentProject(const QString& filePath);

    /**
     * @brief 清除最近打开的项目列表
     */
    void clearRecentProjects();

    /**
     * @brief 更新最近打开的项目菜单
     */
    void updateRecentProjectsMenu();

    /**
     * @brief 添加新的视频流
     */
    void addVideoStream();

    /**
     * @brief 关闭视频流
     * @param widget 要关闭的视频流控件
     */
    void closeVideoStream(VideoStreamWidget* widget);

    /**
     * @brief 浏览视频文件
     */
    void browseVideoFile();

    /**
     * @brief 打开 Python 脚本管理对话框
     */
    void openPythonScriptManager();

    /**
     * @brief 打开 Python 帧处理器配置对话框
     */
    void openPythonFrameProcessorDialog();

    /**
     * @brief 打开 C++ 帧处理器配置对话框
     */
    void openCppFrameProcessorDialog();

    /**
     * @brief 切换帧处理器类型
     * @param useCpp 是否使用C++帧处理器
     */
    void toggleFrameProcessorType(bool useCpp);

    /**
     * @brief 更新帧处理器状态标签
     */
    void updateFrameProcessorStatus();

    /**
     * @brief 打开帧处理脚本编辑器对话框
     */
    void openFrameProcessorEditorDialog();

    /**
     * @brief 重新加载DLL插件
     */
    void reloadDllPlugins();

    /**
     * @brief 设置脚本目录
     */
    void setScriptDirectory();

    /**
     * @brief 浏览本地模型文件
     */
    void browseModelFile();

    /**
     * @brief 浏览网络模型文件
     */
    void browseNetworkModelFile();

    // /**
    //  * @brief 打开摄像头
    //  */
    // void open_camera();

    /**
     * @brief 打开RTSP流
     */
    void open_rtsp_stream();

    /**
     * @brief 切换播放/暂停
     */
    void togglePlayback();

    /**
     * @brief 停止播放
     */
    void stopPlayback();

    /**
     * @brief 更新视频帧
     */
    void updateVideoFrame();

    /**
     * @brief 跳转到指定帧
     * @param position 帧位置
     */
    void seekVideo(int position);

    /**
     * @brief 切换全屏显示
     */
    void toggleFullscreen();

    /**
     * @brief 切换帧存储
     * @param checked 是否选中
     */
    void toggleRecording(bool checked);

    /**
     * @brief 切换视频录制
     * @param checked 是否选中
     */
    void toggleVideoRecording(bool checked);

    /**
     * @brief 设置帧存储路径
     */
    void setRecordingPath();

    /**
     * @brief 设置视频编解码方式
     */
    void toggleVideoDecoderType(bool useHardwareDecoder);

    /**
     * @brief 设置视频录制参数
     */
    void setVideoRecordingParams();

    /**
     * @brief 设置视频存储路径
     */
    void modifyVideoOutputPath();

    /**
     * @brief 重新配置模型参数
     */
    void reconfigureModel();

    /**
     * @brief 更新FPS显示
     */
    void updateFpsDisplay();

    /**
     * @brief 重置计数
     */
    void resetCounts();

    /**
     * @brief 测试当前帧的AI处理
     */
    void testCurrentFrameAI();

    /**
     * @brief 打开结果查看器对话框
     */
    void openResultViewer();

    /**
     * @brief 更新输入输出节点ID
     */
    void updateNodeIds();

    /**
     * @brief 切换结果存储
     * @param checked 是否选中
     */
    void toggleResultStorage(bool checked);

    /**
     * @brief 打开任务管理对话框
     */
    void openTaskManager();

    /**
     * @brief 打开视频抽帧对话框
     */
    void openFrameExtractionDialog();

    /**
     * @brief 执行视频抽帧
     * @param framesPerSecond 每秒抽取的帧数
     */
    void extractFrames(int framesPerSecond);

    /**
     * @brief 打开基于检测结果的抽帧对话框
     */
    void openDetectionBasedExtractionDialog();

    /**
     * @brief 根据检测结果抽取帧
     * @param targetClass 目标类别
     * @param includeTarget 是否包含目标
     */
    void extractFramesByDetection(const QString& targetClass, bool includeTarget);

    /**
     * @brief 打开视频帧编码对话框
     */
    void openFrameEncodingDialog();

    /**
     * @brief 将视频帧编码为视频文件
     * @param inputDir 输入帧目录
     * @param outputFile 输出视频文件
     * @param fps 帧率
     * @param codec 编码器
     * @param framePattern 帧文件名模式
     * @return 是否成功
     */
    bool encodeFramesToVideo(const QString& inputDir, const QString& outputFile, double fps, int codec, const QString& framePattern);

    /**
     * @brief 最小化窗口
     */
    void minimizeWindow();

    /**
     * @brief 最大化或还原窗口
     */
    void toggleMaximizeWindow();

    /**
     * @brief 关闭窗口
     */
    void closeWindow();

    void launchVideoStreamSystem();

    /**
     * @brief 打开算法平台
     */
    void launchVideoAgent();

private:
    /**
     * @brief 设置菜单栏
     */
    void setupMenuBar();

    /**
     * @brief 设置 Python 脚本菜单
     */
    void setupPythonScriptMenu();

    /**
     * @brief 设置插件管理界面
     */
    bool setupPluginManagement();

    /**
     * @brief 自动加载 plugins/task 目录中的所有 DLL 插件
     */
    void autoLoadTaskPlugins();

    /**
     * @brief 确保任务插件已加载，如果未加载则加载
     * @return 是否成功加载插件
     */
    bool ensureTaskPluginsLoaded();

    /**
     * @brief 初始化 Python 脚本管理器
     */
    void initializePythonScriptManager();

    /**
     * @brief 加载用户设置
     */
    void loadSettings();

    /**
     * @brief 保存用户设置
     */
    void saveSettings();

    /**
     * @brief 加载视频
     * @param filePath 视频文件路径
     */
    void loadVideo(const QString& filePath);

    /**
     * @brief 加载模型
     * @param filePath 模型文件路径
     */
    void loadModel(const QString& filePath);

    /**
     * @brief 开始帧录制
     */
    void startRecording();

    /**
     * @brief 停止帧录制
     */
    void stopRecording();

    /**
     * @brief 开始视频录制
     */
    void startVideoRecording();

    /**
     * @brief 停止视频录制
     */
    void stopVideoRecording();

    /**
     * @brief 更新显示帧
     * @param frame 要显示的帧
     */
    void updateDisplayFrame(const cv::Mat& frame);

    /**
     * @brief 获取当前帧的QPixmap
     * @param frame 当前帧
     * @return 帧的QPixmap
     */
    QPixmap get_current_framePixmap(const cv::Mat& frame);

    /**
     * @brief 应用渲染到帧
     * @param frame 要渲染的帧
     * @param ext_info 扩展信息字符串（JSON格式）
     * @return 是否成功应用渲染
     */
    bool applyRenderingToFrame(cv::Mat& frame, const std::string& ext_info);

    /**
     * @brief 把检测框直接渲染在图像上
     * @param frame 要渲染的帧
     * @param result 处理结果，包含检测框
     * @return 是否成功应用渲染
     */
    bool applyRenderingTrackBoxesToFrame(cv::Mat& frame, const ai::FrameResult& result);

private:
    QString formatTime(int seconds);

private:
    // 移除图像存储类型枚举

    // UI组件
    QLineEdit *videoPathEdit;
    QLineEdit *modelPathEdit;
    QLineEdit *inputNodeIdEdit;
    QLineEdit *outputNodeIdEdit;
    QLabel *currentTimeLabel;

    // 视频播放相关
    QGraphicsView *videoDisplay;
    QPushButton *playButton;
    QPushButton *stopButton;
    QSlider *videoSlider;
    QTimer *playTimer;
    bool isPlaying;

    // 处理模式选择
    QRadioButton *noProcessingRadio;
    QRadioButton *aiProcessingRadio;
    QButtonGroup *processingModeGroup;

    // AI处理相关
    bool enableAI;
    int frameSkipInterval;  ///< 抽帧间隔，每隔多少帧进行一次AI检测

    // 帧存储相关
    QCheckBox *frameRecordingCheckBox;
    QCheckBox *videoRecordingCheckBox;
    bool isRecording;
    bool isVideoRecording;
    QString recordingOutputPath;
    int storedFrameCount;
    bool includeRenderingInSavedFrames;  ///< 是否在存储的帧中包含渲染结果

    // 视频录制相关
    cv::VideoWriter videoWriter;
    int videoCodec;
    double videoFps;
    QString videoOutputPath;

    // 全屏相关
    QPushButton *fullscreenButton;
    FullscreenVideoWindow *fullscreenWindow;

    // FPS相关
    QLabel *originalFpsLabel;
    QLabel *currentFpsLabel;
    QTimer *fpsTimer;
    int frameCount;
    qint64 lastFpsUpdateTime;

    // 存储当前帧以便窗口大小变化时使用
    cv::Mat currentFrameMat;

    // 菜单相关
    QMenuBar *menuBar_;

    // 存储用户设置
    QString lastPrompt;
    double lastScore;
    int lastIou;

    // 任务相关
    bool enableCounting;
    // 移除以下成员变量:
    // bool enableEventDetection;

    // 结果存储和查看相关
    bool enableResultStorage;
    int resultStorageTcpPort;                                 ///< 结果存储TCP端口
    core::VideoResultStorageServer::StorageMode resultStorageMode; ///< 结果存储模式
    int resultStorageFlushInterval;                          ///< 结果存储刷新间隔
    core::protocols::ProtocolType resultStorageProtocolType;  ///< 结果存储协议类型
    std::unordered_map<std::string, uint16_t> modbusRegisterMap; ///< Modbus寄存器映射

    // MQTT配置参数
    QString mqttBrokerAddress;                               ///< MQTT Broker地址
    int mqttBrokerPort;                                      ///< MQTT Broker端口
    QString mqttUsername;                                    ///< MQTT用户名
    QString mqttPassword;                                    ///< MQTT密码
    QString mqttTopic;                                       ///< MQTT主题
    int mqttQos;                                             ///< MQTT QoS等级

    std::unique_ptr<ui::ResultViewerDialog> resultViewerDialog;

    // 任务管理相关
    std::unique_ptr<ui::TaskManagerDialog> taskManagerDialog;

    // 区域绘制相关
    // 移除以下成员变量:
    // std::vector<cv::Point> restrictedArea;
    // std::vector<cv::Point> monitoringArea;
    // QRadioButton* eventDetectionRadio;

    // 核心组件
    std::shared_ptr<core::VideoProcessingCore> videoProcessingCore;

    // 插件管理器
    std::shared_ptr<ai::plugins::PluginManager> pluginManager;

    // Python脚本管理器
    std::shared_ptr<ai::plugins::PythonScriptManager> pythonScriptManager;

    // 插件加载状态跟踪
    bool taskPluginsLoaded = false;

    // 帧历史设置
    static constexpr int DEFAULT_MAX_FRAME_HISTORY = 30;
    int maxFrameHistory;

    // 帧处理器状态标签
    QLabel *frameProcessorStatusLabel;

    // 插件渲染器
    std::unique_ptr<utils::QPluginRenderer> pluginRenderer;

    // 用户定义的类别名称
    QStringList userDefinedClassNames;

    // 项目相关
    QString currentProjectPath;  ///< 当前项目路径
    QMenu* recentProjectsMenu;   ///< 最近打开的项目菜单
    QList<QAction*> recentProjectActions;  ///< 最近打开的项目动作列表

    // 多路视频流相关
    QWidget* videoStreamsContainer;  ///< 视频流容器
    QList<VideoStreamWidget*> videoStreams;  ///< 视频流控件列表
    QGridLayout* videoStreamsLayout;  ///< 视频流布局

    // 无标题栏窗口拖动相关
    bool isDragging;             ///< 是否正在拖动窗口
    QPoint dragPosition;         ///< 拖动起始位置

    // 自定义标题栏控制按钮
    QPushButton* minimizeButton; ///< 最小化按钮
    QPushButton* maximizeButton; ///< 最大化/还原按钮
    QPushButton* closeButton;    ///< 关闭按钮
    QWidget* titleContainer;     ///< 标题容器

public:
    /**
     * @brief 设置最大帧历史数量
     * @param count 最大帧数
     */
    void set_max_frame_history(int count);

    /**
     * @brief 获取最大帧历史数量
     * @return 最大帧数
     */
    int getMaxFrameHistory() const;

    /**
     * @brief 启用AI处理模式
     * @param enable 是否启用
     */
    void enableAIProcessingMode(bool enable);
};

} // namespace ui





